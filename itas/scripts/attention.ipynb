import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from IPython.display import display, HTML
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("Libraries loaded successfully!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "meta-llama/Llama-3.1-8B"

# Load model and tokenizer
print(f"Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None
)

# Add padding token if not present
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"Model loaded on device: {device}")
print(f"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads")

# Load dataset
def load_dataset(path):
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

dataset = load_dataset(DATASET_PATH)
print(f"Loaded {len(dataset)} examples from dataset")

# Display first example structure
print("\nExample structure:")
example = dataset[0]
for key, value in example.items():
    if isinstance(value, str) and len(value) > 100:
        print(f"{key}: {value[:100]}...")
    else:
        print(f"{key}: {value}")

def prepare_inputs(system_prompt, user_prompt, max_length=512):
    """Prepare inputs for the model using chat template."""
    # Create conversation in chat format
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    # Apply chat template
    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:
        formatted_prompt = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
    else:
        # Fallback to manual formatting if no chat template
        formatted_prompt = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"

    # Tokenize
    inputs = tokenizer(
        formatted_prompt,
        return_tensors="pt",
        max_length=max_length,
        truncation=True,
        padding=True
    )

    return inputs, formatted_prompt

def extract_attention_patterns(inputs, num_tokens_to_generate=10):
    """Extract attention patterns and calculate perplexity during text generation."""
    model.eval()

    with torch.no_grad():
        # Move inputs to device
        input_ids = inputs['input_ids'].to(device)
        attention_mask = inputs['attention_mask'].to(device)

        # Generate tokens and collect attention + perplexity
        generated_tokens = []
        all_attentions = []
        token_log_probs = []

        current_input_ids = input_ids
        current_attention_mask = attention_mask

        for step in range(num_tokens_to_generate):
            # Forward pass
            outputs = model(
                input_ids=current_input_ids,
                attention_mask=current_attention_mask,
                output_attentions=True
            )

            # Get next token probabilities
            logits = outputs.logits[0, -1, :]
            log_probs = torch.log_softmax(logits, dim=-1)
            next_token_id = torch.argmax(logits, dim=-1).unsqueeze(0).unsqueeze(0)

            # Store log probability of selected token
            token_log_prob = log_probs[next_token_id.item()].item()
            token_log_probs.append(token_log_prob)

            # Store attention (last layer, average across heads)
            last_layer_attention = outputs.attentions[-1][0]  # [num_heads, seq_len, seq_len]
            avg_attention = last_layer_attention.mean(dim=0)  # [seq_len, seq_len]
            all_attentions.append(avg_attention[-1, :].cpu().numpy())  # Attention from last token

            # Store generated token
            generated_tokens.append(next_token_id.item())

            # Update inputs for next iteration
            current_input_ids = torch.cat([current_input_ids, next_token_id], dim=1)
            current_attention_mask = torch.cat([
                current_attention_mask,
                torch.ones(1, 1, device=device)
            ], dim=1)

            # Stop if EOS token
            if next_token_id.item() == tokenizer.eos_token_id:
                break

    # Calculate perplexity
    if token_log_probs:
        avg_log_prob = np.mean(token_log_probs)
        perplexity = np.exp(-avg_log_prob)
    else:
        perplexity = float('inf')

    # Pad attention arrays to have consistent shape
    if all_attentions:
        max_seq_len = max(len(att) for att in all_attentions)
        padded_attentions = []
        for att in all_attentions:
            if len(att) < max_seq_len:
                # Pad with zeros
                padded_att = np.zeros(max_seq_len)
                padded_att[:len(att)] = att
                padded_attentions.append(padded_att)
            else:
                padded_attentions.append(att)
        attention_array = np.array(padded_attentions)
    else:
        attention_array = np.array([])

    return attention_array, generated_tokens, token_log_probs, perplexity

print("Attention extraction functions defined!")

def create_attention_heatmap(attention_weights, input_tokens, generated_tokens, title, ax):
    """Create attention heatmap visualization."""
    # Prepare data for heatmap
    attention_matrix = attention_weights[:len(generated_tokens), :len(input_tokens)]

    # Create heatmap
    im = ax.imshow(attention_matrix, cmap='Blues', aspect='auto')

    # Set labels
    ax.set_xlabel('Input Tokens')
    ax.set_ylabel('Generated Tokens')
    ax.set_title(title)

    # Set tick labels (show every 5th token to avoid crowding)
    input_labels = [token[:10] + '...' if len(token) > 10 else token for token in input_tokens]
    generated_labels = [tokenizer.decode([token]) for token in generated_tokens]

    step = max(1, len(input_tokens) // 20)  # Show ~20 labels max
    ax.set_xticks(range(0, len(input_tokens), step))
    ax.set_xticklabels([input_labels[i] for i in range(0, len(input_tokens), step)],
                       rotation=45, ha='right')

    ax.set_yticks(range(len(generated_tokens)))
    ax.set_yticklabels(generated_labels)

    # Add colorbar
    plt.colorbar(im, ax=ax, label='Attention Weight')

    return im

def create_attention_flow_visualization(attention_weights, input_tokens, generated_tokens, title, ax, threshold=0.1):
    """Create attention flow visualization with line connections."""
    if len(attention_weights) == 0 or len(generated_tokens) == 0:
        ax.text(0.5, 0.5, 'No attention data available', ha='center', va='center', transform=ax.transAxes)
        ax.set_title(title)
        return

    # Prepare tokens for display
    input_labels = [token[:8] + '...' if len(token) > 8 else token for token in input_tokens]
    generated_labels = [tokenizer.decode([token])[:8] + '...' if len(tokenizer.decode([token])) > 8
                       else tokenizer.decode([token]) for token in generated_tokens]

    # Limit to reasonable number of tokens for visualization
    max_input_tokens = 30
    max_generated_tokens = min(len(generated_tokens), 8)

    if len(input_tokens) > max_input_tokens:
        # Sample tokens from different parts of the input
        indices = np.linspace(0, len(input_tokens)-1, max_input_tokens, dtype=int)
        input_labels = [input_labels[i] for i in indices]
        attention_subset = attention_weights[:max_generated_tokens, indices]
    else:
        attention_subset = attention_weights[:max_generated_tokens, :len(input_tokens)]

    generated_labels = generated_labels[:max_generated_tokens]

    # Set up positions
    input_y_positions = np.arange(len(input_labels))
    generated_y_positions = np.arange(len(generated_labels))

    # Clear the axes
    ax.clear()

    # Draw input tokens on the left
    for i, label in enumerate(input_labels):
        ax.text(0, input_y_positions[i], label, ha='right', va='center', fontsize=8,
               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7))

    # Draw generated tokens on the right
    for i, label in enumerate(generated_labels):
        ax.text(2, generated_y_positions[i], label, ha='left', va='center', fontsize=8,
               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.7))

    # Draw attention connections
    for gen_idx in range(len(generated_labels)):
        for inp_idx in range(len(input_labels)):
            if gen_idx < attention_subset.shape[0] and inp_idx < attention_subset.shape[1]:
                attention_weight = attention_subset[gen_idx, inp_idx]

                if attention_weight > threshold:
                    # Draw line with thickness and opacity based on attention weight
                    line_width = max(0.5, attention_weight * 5)  # Scale line width
                    alpha = min(1.0, attention_weight * 3)  # Scale transparency

                    ax.plot([0.1, 1.9],
                           [input_y_positions[inp_idx], generated_y_positions[gen_idx]],
                           linewidth=line_width, alpha=alpha, color='red')

                    # Add attention weight as text on the line (for strongest connections)
                    if attention_weight > threshold * 3:
                        mid_x = 1.0
                        mid_y = (input_y_positions[inp_idx] + generated_y_positions[gen_idx]) / 2
                        ax.text(mid_x, mid_y, f'{attention_weight:.2f}',
                               ha='center', va='center', fontsize=6,
                               bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.8))

    # Set up the plot
    ax.set_xlim(-0.5, 2.5)
    ax.set_ylim(-0.5, max(len(input_labels), len(generated_labels)) - 0.5)
    ax.set_title(title)
    ax.set_xlabel('Input Tokens → Generated Tokens')
    ax.axis('off')  # Remove axes for cleaner look

    # Add legend
    ax.text(1, -0.3, f'Line thickness/opacity ∝ attention weight (threshold: {threshold})',
           ha='center', va='top', fontsize=8, style='italic')

    return ax

def analyze_attention_differences(attention_a, attention_b, input_tokens, threshold=0.1):
    """Analyze differences between two attention patterns."""
    # Average attention across generated tokens
    avg_attention_a = np.mean(attention_a, axis=0)
    avg_attention_b = np.mean(attention_b, axis=0)

    # Calculate difference
    attention_diff = avg_attention_a - avg_attention_b

    # Find tokens with significant differences
    significant_indices = np.where(np.abs(attention_diff) > threshold)[0]

    results = []
    for idx in significant_indices:
        if idx < len(input_tokens):
            results.append({
                'token': input_tokens[idx],
                'position': idx,
                'scheming_attention': avg_attention_a[idx],
                'baseline_attention': avg_attention_b[idx],
                'difference': attention_diff[idx]
            })

    return sorted(results, key=lambda x: abs(x['difference']), reverse=True)

def create_perplexity_comparison(perplexity_a, perplexity_b, log_probs_a, log_probs_b, generated_tokens_a, generated_tokens_b, ax):
    """Create perplexity comparison visualization."""
    # Create bar chart for overall perplexity
    scenarios = ['Scheming', 'Baseline']
    perplexities = [perplexity_a, perplexity_b]
    colors = ['red', 'blue']

    bars = ax.bar(scenarios, perplexities, color=colors, alpha=0.7)
    ax.set_ylabel('Perplexity')
    ax.set_title('Answer Token Perplexity Comparison')

    # Add value labels on bars
    for bar, perp in zip(bars, perplexities):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{perp:.2f}', ha='center', va='bottom')

    return bars

def create_token_level_perplexity(log_probs_a, log_probs_b, generated_tokens_a, generated_tokens_b, ax):
    """Create token-level perplexity visualization."""
    # Convert log probs to perplexity for each token
    token_perps_a = [np.exp(-lp) for lp in log_probs_a]
    token_perps_b = [np.exp(-lp) for lp in log_probs_b]

    # Get token strings
    tokens_a = [tokenizer.decode([token]) for token in generated_tokens_a[:len(token_perps_a)]]
    tokens_b = [tokenizer.decode([token]) for token in generated_tokens_b[:len(token_perps_b)]]

    # Plot token-level perplexities
    max_len = max(len(token_perps_a), len(token_perps_b))
    x_pos = np.arange(max_len)

    if len(token_perps_a) > 0:
        ax.plot(range(len(token_perps_a)), token_perps_a, 'ro-', label='Scheming', alpha=0.7)
    if len(token_perps_b) > 0:
        ax.plot(range(len(token_perps_b)), token_perps_b, 'bo-', label='Baseline', alpha=0.7)

    ax.set_xlabel('Token Position')
    ax.set_ylabel('Token Perplexity')
    ax.set_title('Token-Level Perplexity')
    ax.legend()

    # Add token labels if not too many
    if max_len <= 10:
        combined_tokens = tokens_a + [''] * (max_len - len(tokens_a)) if len(tokens_a) < max_len else tokens_a
        ax.set_xticks(range(max_len))
        ax.set_xticklabels([t[:8] + '...' if len(t) > 8 else t for t in combined_tokens], rotation=45)

    return ax

print("Visualization functions defined!")

def analyze_example(example_idx):
    """Analyze attention patterns for a specific example."""
    if example_idx >= len(dataset):
        print(f"Invalid example index. Dataset has {len(dataset)} examples.")
        return

    example = dataset[example_idx]

    print(f"\n=== Example {example_idx} ===")
    print(f"Pressure Category: {example.get('pressure_category', 'N/A')}")
    print(f"Pressure Source: {example.get('pressure_source', 'N/A')}")
    print(f"Ground Truth: {example['ground_truth']}")
    print(f"Honest Answer: {example['honest']}")
    print(f"Dishonest Answer: {example['dishonest']}")

    # Scenario A: Scheming (original prompts)
    inputs_a, prompt_a = prepare_inputs(
        example['system_prompt'],
        example['user_prompt']
    )

    # Scenario B: Baseline (neutral system + original context)
    baseline_system = "You are a helpful assistant."
    baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    inputs_b, prompt_b = prepare_inputs(baseline_system, baseline_user)

    print("\n--- Scenario A (Scheming) ---")
    print(f"System: {example['system_prompt'][:100]}...")
    print(f"User: {example['user_prompt'][:100]}...")

    print("\n--- Scenario B (Baseline) ---")
    print(f"System: {baseline_system}")
    print(f"User: {baseline_user[:100]}...")

    # Extract attention patterns
    print("\nExtracting attention patterns...")

    # Scenario A
    attention_a, generated_a, log_probs_a, perplexity_a = extract_attention_patterns(inputs_a)
    tokens_a = tokenizer.convert_ids_to_tokens(inputs_a['input_ids'][0])

    # Scenario B
    attention_b, generated_b, log_probs_b, perplexity_b = extract_attention_patterns(inputs_b)
    tokens_b = tokenizer.convert_ids_to_tokens(inputs_b['input_ids'][0])

    print(f"\nPerplexity - Scheming: {perplexity_a:.2f}, Baseline: {perplexity_b:.2f}")

    # Create visualizations
    fig, ((ax1, ax2), (ax3, ax4), (ax5, ax6)) = plt.subplots(3, 2, figsize=(20, 18))

    # Attention flow visualizations
    create_attention_flow_visualization(
        attention_a, tokens_a, generated_a,
        "Scenario A: Scheming Attention Flow", ax1, threshold=0.05
    )

    create_attention_flow_visualization(
        attention_b, tokens_b, generated_b,
        "Scenario B: Baseline Attention Flow", ax2, threshold=0.05
    )

    # Average attention comparison
    avg_attention_a = np.mean(attention_a, axis=0)
    avg_attention_b = np.mean(attention_b, axis=0)

    # Plot average attention weights
    min_len = min(len(avg_attention_a), len(avg_attention_b))
    x_pos = np.arange(min_len)

    ax3.bar(x_pos - 0.2, avg_attention_a[:min_len], 0.4, label='Scheming', alpha=0.7)
    ax3.bar(x_pos + 0.2, avg_attention_b[:min_len], 0.4, label='Baseline', alpha=0.7)
    ax3.set_xlabel('Token Position')
    ax3.set_ylabel('Average Attention Weight')
    ax3.set_title('Average Attention Weights Comparison')
    ax3.legend()

    # Attention difference
    attention_diff = avg_attention_a[:min_len] - avg_attention_b[:min_len]
    colors = ['red' if x > 0 else 'blue' for x in attention_diff]
    ax4.bar(x_pos, attention_diff, color=colors, alpha=0.7)
    ax4.set_xlabel('Token Position')
    ax4.set_ylabel('Attention Difference (Scheming - Baseline)')
    ax4.set_title('Attention Pattern Differences')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)

    # Perplexity comparison
    create_perplexity_comparison(perplexity_a, perplexity_b, log_probs_a, log_probs_b,
                                generated_a, generated_b, ax5)

    # Token-level perplexity
    create_token_level_perplexity(log_probs_a, log_probs_b, generated_a, generated_b, ax6)

    plt.tight_layout()
    plt.show()

    # Analyze significant differences
    if len(tokens_a) == len(tokens_b):  # Only if same tokenization
        differences = analyze_attention_differences(attention_a, attention_b, tokens_a)

        if differences:
            print("\n=== Top Attention Differences ===")
            for i, diff in enumerate(differences[:10]):
                direction = "MORE" if diff['difference'] > 0 else "LESS"
                print(f"{i+1}. Token '{diff['token']}' (pos {diff['position']}): "
                      f"{direction} attention in scheming ({diff['difference']:.4f})")

    return {
        'attention_scheming': attention_a,
        'attention_baseline': attention_b,
        'tokens_scheming': tokens_a,
        'tokens_baseline': tokens_b,
        'generated_scheming': generated_a,
        'generated_baseline': generated_b,
        'log_probs_scheming': log_probs_a,
        'log_probs_baseline': log_probs_b,
        'perplexity_scheming': perplexity_a,
        'perplexity_baseline': perplexity_b
    }

print("Analysis function defined!")

# Create interactive widget
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset)-1, 50),  # Limit to first 50 examples for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

# Create category filter
categories = list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:51]]))
category_dropdown = widgets.Dropdown(
    options=['All'] + sorted(categories),
    value='All',
    description='Category:',
    style={'description_width': 'initial'}
)

def update_example_range(*args):
    """Update available examples based on category filter."""
    if category_dropdown.value == 'All':
        example_slider.max = min(len(dataset)-1, 50)
    else:
        filtered_indices = [i for i, ex in enumerate(dataset[:51])
                          if ex.get('pressure_category') == category_dropdown.value]
        if filtered_indices:
            example_slider.max = max(filtered_indices)
            example_slider.value = filtered_indices[0]

category_dropdown.observe(update_example_range, names='value')

# Display widgets
display(widgets.VBox([category_dropdown, example_slider]))

print("Interactive widgets created! Use the sliders above to select examples for analysis.")

# Run analysis on currently selected example
results = analyze_example(example_slider.value)

def batch_attention_analysis(num_examples=10):
    """Analyze attention patterns across multiple examples."""
    print(f"Analyzing attention patterns across {num_examples} examples...")

    all_differences = []
    category_differences = {}

    for i in range(min(num_examples, len(dataset))):
        example = dataset[i]
        category = example.get('pressure_category', 'unknown')

        try:
            # Prepare inputs for both scenarios
            inputs_a, _ = prepare_inputs(example['system_prompt'], example['user_prompt'])

            baseline_system = "You are a helpful assistant."
            baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
            inputs_b, _ = prepare_inputs(baseline_system, baseline_user)

            # Extract attention patterns and perplexity
            attention_a, _, _, perplexity_a = extract_attention_patterns(inputs_a, num_tokens_to_generate=5)
            attention_b, _, _, perplexity_b = extract_attention_patterns(inputs_b, num_tokens_to_generate=5)

            # Calculate average attention difference
            avg_attention_a = np.mean(attention_a, axis=0)
            avg_attention_b = np.mean(attention_b, axis=0)

            min_len = min(len(avg_attention_a), len(avg_attention_b))
            attention_diff = np.mean(np.abs(avg_attention_a[:min_len] - avg_attention_b[:min_len]))

            all_differences.append({
                'example_id': i,
                'category': category,
                'attention_difference': attention_diff,
                'perplexity_scheming': perplexity_a,
                'perplexity_baseline': perplexity_b,
                'perplexity_difference': perplexity_a - perplexity_b,
                'pressure_source': example.get('pressure_source', 'unknown')
            })

            # Group by category
            if category not in category_differences:
                category_differences[category] = []
            category_differences[category].append(attention_diff)

            if (i + 1) % 5 == 0:
                print(f"Processed {i + 1}/{num_examples} examples...")

        except Exception as e:
            print(f"Error processing example {i}: {e}")
            continue

    # Create summary visualizations
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))

    # Plot 1: Attention differences by example
    df = pd.DataFrame(all_differences)
    ax1.scatter(df['example_id'], df['attention_difference'],
               c=pd.Categorical(df['category']).codes, alpha=0.7)
    ax1.set_xlabel('Example ID')
    ax1.set_ylabel('Average Attention Difference')
    ax1.set_title('Attention Pattern Differences Across Examples')

    # Plot 2: Attention differences by category
    categories = list(category_differences.keys())
    avg_diffs = [np.mean(category_differences[cat]) for cat in categories]
    std_diffs = [np.std(category_differences[cat]) for cat in categories]

    ax2.bar(range(len(categories)), avg_diffs, yerr=std_diffs, capsize=5)
    ax2.set_xlabel('Pressure Category')
    ax2.set_ylabel('Average Attention Difference')
    ax2.set_title('Attention Differences by Pressure Category')
    ax2.set_xticks(range(len(categories)))
    ax2.set_xticklabels(categories, rotation=45, ha='right')

    # Plot 3: Perplexity differences by example
    ax3.scatter(df['example_id'], df['perplexity_difference'],
               c=pd.Categorical(df['category']).codes, alpha=0.7)
    ax3.set_xlabel('Example ID')
    ax3.set_ylabel('Perplexity Difference (Scheming - Baseline)')
    ax3.set_title('Perplexity Differences Across Examples')
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # Plot 4: Perplexity comparison by category
    perp_scheming = [np.mean([d['perplexity_scheming'] for d in all_differences if d['category'] == cat]) for cat in categories]
    perp_baseline = [np.mean([d['perplexity_baseline'] for d in all_differences if d['category'] == cat]) for cat in categories]

    x_pos = np.arange(len(categories))
    width = 0.35

    ax4.bar(x_pos - width/2, perp_scheming, width, label='Scheming', alpha=0.7, color='red')
    ax4.bar(x_pos + width/2, perp_baseline, width, label='Baseline', alpha=0.7, color='blue')
    ax4.set_xlabel('Pressure Category')
    ax4.set_ylabel('Average Perplexity')
    ax4.set_title('Perplexity by Category and Scenario')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels(categories, rotation=45, ha='right')
    ax4.legend()

    plt.tight_layout()
    plt.show()

    # Print summary statistics
    print("\n=== Summary Statistics ===")
    print(f"Overall average attention difference: {np.mean([d['attention_difference'] for d in all_differences]):.4f}")
    print(f"Standard deviation: {np.std([d['attention_difference'] for d in all_differences]):.4f}")

    print(f"\nOverall average perplexity difference (Scheming - Baseline): {np.mean([d['perplexity_difference'] for d in all_differences]):.4f}")
    print(f"Average scheming perplexity: {np.mean([d['perplexity_scheming'] for d in all_differences]):.4f}")
    print(f"Average baseline perplexity: {np.mean([d['perplexity_baseline'] for d in all_differences]):.4f}")

    print("\n=== By Category ===")
    for category in sorted(category_differences.keys()):
        cat_data = [d for d in all_differences if d['category'] == category]
        att_diffs = [d['attention_difference'] for d in cat_data]
        perp_diffs = [d['perplexity_difference'] for d in cat_data]
        perp_scheming = [d['perplexity_scheming'] for d in cat_data]
        perp_baseline = [d['perplexity_baseline'] for d in cat_data]

        print(f"{category}:")
        print(f"  Attention diff: {np.mean(att_diffs):.4f} ± {np.std(att_diffs):.4f}")
        print(f"  Perplexity diff: {np.mean(perp_diffs):.4f} ± {np.std(perp_diffs):.4f}")
        print(f"  Scheming perp: {np.mean(perp_scheming):.4f}, Baseline perp: {np.mean(perp_baseline):.4f} (n={len(cat_data)})")

    return all_differences, category_differences

print("Batch analysis function defined!")
print("\nTo run batch analysis, execute: batch_results = batch_attention_analysis(10)")