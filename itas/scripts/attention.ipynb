import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from IPython.display import display, HTML
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
from scipy import stats
from sklearn.metrics.pairwise import cosine_similarity
import re

warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("📚 Libraries loaded successfully!")
print("🎨 Advanced visualization tools ready!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "meta-llama/Llama-3.1-8B"

# Load model and tokenizer with optimized settings
print(f"🤖 Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None,
    use_cache=False  # Disable cache for attention analysis
)

# Configure tokenizer
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"🔧 Model loaded on device: {device}")
print(f"📊 Model architecture: {model.config.num_hidden_layers} layers, {model.config.num_attention_heads} heads")
print(f"🧠 Memory usage: {torch.cuda.memory_allocated()/1e9:.2f}GB" if torch.cuda.is_available() else "CPU mode")

def clean_llama_tokens(tokens):
    """Clean up Llama tokenizer tokens by removing the 'Ġ' character that represents spaces."""
    cleaned_tokens = []
    for token in tokens:
        if token.startswith('Ġ'):
            # Replace 'Ġ' with space and strip to avoid leading spaces
            cleaned_token = token.replace('Ġ', ' ').strip()
            # If the token becomes empty after cleaning, use the original
            cleaned_tokens.append(cleaned_token if cleaned_token else token)
        else:
            cleaned_tokens.append(token)
    return cleaned_tokens

def get_readable_tokens(input_ids, tokenizer):
    """Convert token IDs to readable tokens with proper space handling."""
    raw_tokens = tokenizer.convert_ids_to_tokens(input_ids)
    return clean_llama_tokens(raw_tokens)

def truncate_token_for_display(token, max_length=12):
    """Truncate tokens for display purposes."""
    if len(token) > max_length:
        return token[:max_length-3] + "..."
    return token

def identify_token_types(tokens):
    """Classify tokens by type for better visualization."""
    token_types = []
    for token in tokens:
        if token.startswith('<|') and token.endswith('|>'):
            token_types.append('special')
        elif token.startswith('<') and token.endswith('>'):
            token_types.append('tag')
        elif token.isalpha():
            token_types.append('word')
        elif token.isdigit():
            token_types.append('number')
        elif token in '.,!?;:':
            token_types.append('punctuation')
        else:
            token_types.append('other')
    return token_types

print("🔧 Token processing utilities defined!")

# Test the token cleaning
test_text = "Hello world! This is a test."
test_tokens = tokenizer.tokenize(test_text)
cleaned_tokens = clean_llama_tokens(test_tokens)
print(f"\n🧪 Token cleaning test:")
print(f"Raw tokens: {test_tokens[:5]}...")
print(f"Cleaned tokens: {cleaned_tokens[:5]}...")

def load_dataset(path):
    """Load and validate dataset."""
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data.append(json.loads(line.strip()))
            except json.JSONDecodeError as e:
                print(f"⚠️ Warning: Invalid JSON on line {line_num}: {e}")
                continue
    return data

# Load and analyze dataset
dataset = load_dataset(DATASET_PATH)
print(f"📁 Loaded {len(dataset)} examples from dataset")

# Analyze dataset composition
categories = [ex.get('pressure_category', 'unknown') for ex in dataset]
category_counts = pd.Series(categories).value_counts()
print(f"\n📊 Dataset composition:")
for cat, count in category_counts.items():
    print(f"  {cat}: {count} examples ({count/len(dataset)*100:.1f}%)")

# Display example structure
print("\n🔍 Example structure:")
example = dataset[0]
for key, value in example.items():
    if isinstance(value, str) and len(value) > 100:
        print(f"  {key}: {value[:100]}...")
    else:
        print(f"  {key}: {value}")

def prepare_inputs(system_prompt, user_prompt, max_length=512):
    """Prepare inputs for the model using chat template."""
    # Create conversation in chat format
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    # Apply chat template
    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:
        formatted_prompt = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
    else:
        # Fallback to manual formatting if no chat template
        formatted_prompt = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"

    # Tokenize
    inputs = tokenizer(
        formatted_prompt,
        return_tensors="pt",
        max_length=max_length,
        truncation=True,
        padding=True
    )

    return inputs, formatted_prompt

def extract_multi_layer_attention(inputs, num_tokens_to_generate=10, layers_to_analyze=None):
    """Extract attention patterns from multiple layers during text generation."""
    model.eval()

    if layers_to_analyze is None:
        # Analyze last 4 layers by default
        layers_to_analyze = list(range(model.config.num_hidden_layers-4, model.config.num_hidden_layers))

    with torch.no_grad():
        # Move inputs to device
        input_ids = inputs['input_ids'].to(device)
        attention_mask = inputs['attention_mask'].to(device)

        # Generate tokens and collect multi-layer attention
        generated_tokens = []
        all_layer_attentions = {layer: [] for layer in layers_to_analyze}
        token_log_probs = []

        current_input_ids = input_ids
        current_attention_mask = attention_mask

        for step in range(num_tokens_to_generate):
            # Forward pass
            outputs = model(
                input_ids=current_input_ids,
                attention_mask=current_attention_mask,
                output_attentions=True
            )

            # Get next token probabilities
            logits = outputs.logits[0, -1, :]
            log_probs = torch.log_softmax(logits, dim=-1)
            next_token_id = torch.argmax(logits, dim=-1).unsqueeze(0).unsqueeze(0)

            # Store log probability of selected token
            token_log_prob = log_probs[next_token_id.item()].item()
            token_log_probs.append(token_log_prob)

            # Store attention from specified layers
            for layer_idx in layers_to_analyze:
                layer_attention = outputs.attentions[layer_idx][0]  # [num_heads, seq_len, seq_len]
                # Store both head-averaged and individual head attention
                avg_attention = layer_attention.mean(dim=0)  # [seq_len, seq_len]
                all_layer_attentions[layer_idx].append({
                    'avg_attention': avg_attention[-1, :].cpu().numpy(),  # Attention from last token
                    'head_attentions': layer_attention[:, -1, :].cpu().numpy()  # All heads from last token
                })

            # Store generated token
            generated_tokens.append(next_token_id.item())

            # Update inputs for next iteration
            current_input_ids = torch.cat([current_input_ids, next_token_id], dim=1)
            current_attention_mask = torch.cat([
                current_attention_mask,
                torch.ones(1, 1, device=device)
            ], dim=1)

            # Stop if EOS token
            if next_token_id.item() == tokenizer.eos_token_id:
                break

    # Calculate perplexity
    if token_log_probs:
        avg_log_prob = np.mean(token_log_probs)
        perplexity = np.exp(-avg_log_prob)
    else:
        perplexity = float('inf')

    return all_layer_attentions, generated_tokens, token_log_probs, perplexity

print("🔬 Advanced attention extraction functions defined!")

def create_interactive_attention_heatmap(attention_weights, input_tokens, generated_tokens, title):
    """Create an interactive attention heatmap using Plotly."""
    # Prepare data
    attention_matrix = attention_weights[:len(generated_tokens), :len(input_tokens)]

    # Clean and truncate tokens for display
    input_labels = [truncate_token_for_display(token) for token in input_tokens]
    generated_labels = [tokenizer.decode([token]) for token in generated_tokens]

    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=attention_matrix,
        x=input_labels,
        y=generated_labels,
        colorscale='Blues',
        hoverongaps=False,
        hovertemplate='<b>Input Token:</b> %{x}<br><b>Generated Token:</b> %{y}<br><b>Attention:</b> %{z:.4f}<extra></extra>'
    ))

    fig.update_layout(
        title=title,
        xaxis_title="Input Tokens",
        yaxis_title="Generated Tokens",
        width=1000,
        height=600
    )

    return fig

def create_multi_layer_comparison(layer_attentions_scheming, layer_attentions_baseline,
                                input_tokens, layers_to_show=None):
    """Create multi-layer attention comparison visualization."""
    if layers_to_show is None:
        layers_to_show = list(layer_attentions_scheming.keys())[:4]

    # Create subplots
    fig = make_subplots(
        rows=2, cols=len(layers_to_show),
        subplot_titles=[f"Layer {layer} - Scheming" for layer in layers_to_show] +
                      [f"Layer {layer} - Baseline" for layer in layers_to_show],
        vertical_spacing=0.1,
        horizontal_spacing=0.05
    )

    # Clean tokens for display
    input_labels = [truncate_token_for_display(token, 8) for token in input_tokens]

    for i, layer in enumerate(layers_to_show):
        # Average attention across generated tokens for each layer - handle variable lengths
        if layer_attentions_scheming[layer]:
            attention_arrays = [step['avg_attention'] for step in layer_attentions_scheming[layer]]
            if attention_arrays:
                # Find minimum length and truncate
                min_len = min(len(arr) for arr in attention_arrays)
                truncated_arrays = [arr[:min_len] for arr in attention_arrays]
                scheming_avg = np.mean(truncated_arrays, axis=0)
                scheming_avg = scheming_avg[:len(input_tokens)]
            else:
                scheming_avg = np.zeros(len(input_tokens))
        else:
            scheming_avg = np.zeros(len(input_tokens))

        if layer_attentions_baseline[layer]:
            attention_arrays = [step['avg_attention'] for step in layer_attentions_baseline[layer]]
            if attention_arrays:
                # Find minimum length and truncate
                min_len = min(len(arr) for arr in attention_arrays)
                truncated_arrays = [arr[:min_len] for arr in attention_arrays]
                baseline_avg = np.mean(truncated_arrays, axis=0)
                baseline_avg = baseline_avg[:len(input_tokens)]
            else:
                baseline_avg = np.zeros(len(input_tokens))
        else:
            baseline_avg = np.zeros(len(input_tokens))

        # Add scheming attention (top row)
        fig.add_trace(
            go.Bar(x=input_labels, y=scheming_avg, name=f"Layer {layer} Scheming",
                  marker_color='red', opacity=0.7),
            row=1, col=i+1
        )

        # Add baseline attention (bottom row)
        fig.add_trace(
            go.Bar(x=input_labels, y=baseline_avg, name=f"Layer {layer} Baseline",
                  marker_color='blue', opacity=0.7),
            row=2, col=i+1
        )

    fig.update_layout(
        title="Multi-Layer Attention Comparison: Scheming vs Baseline",
        showlegend=False,
        height=800,
        width=1400
    )

    # Update x-axis labels to be rotated
    fig.update_xaxes(tickangle=45)

    return fig

def create_attention_head_analysis(layer_attentions, input_tokens, layer_idx, num_heads_to_show=8):
    """Analyze individual attention heads within a layer."""
    if not layer_attentions[layer_idx]:
        return None

    # Get head attentions from first generation step
    head_attentions = layer_attentions[layer_idx][0]['head_attentions']  # [num_heads, seq_len]
    head_attentions = head_attentions[:, :len(input_tokens)]  # Trim to input length

    # Select most diverse heads
    head_similarities = cosine_similarity(head_attentions)
    selected_heads = [0]  # Always include first head

    for _ in range(min(num_heads_to_show-1, head_attentions.shape[0]-1)):
        # Find head with lowest average similarity to selected heads
        remaining_heads = [h for h in range(head_attentions.shape[0]) if h not in selected_heads]
        if not remaining_heads:
            break

        similarities = []
        for h in remaining_heads:
            avg_sim = np.mean([head_similarities[h, s] for s in selected_heads])
            similarities.append((avg_sim, h))

        # Select head with lowest similarity
        selected_heads.append(min(similarities)[1])

    # Create visualization
    input_labels = [truncate_token_for_display(token, 10) for token in input_tokens]

    fig = go.Figure()

    colors = px.colors.qualitative.Set3
    for i, head_idx in enumerate(selected_heads):
        fig.add_trace(go.Scatter(
            x=input_labels,
            y=head_attentions[head_idx],
            mode='lines+markers',
            name=f'Head {head_idx}',
            line=dict(color=colors[i % len(colors)], width=2),
            marker=dict(size=6)
        ))

    fig.update_layout(
        title=f"Individual Attention Heads Analysis - Layer {layer_idx}",
        xaxis_title="Input Tokens",
        yaxis_title="Attention Weight",
        width=1200,
        height=600,
        xaxis_tickangle=45
    )

    return fig

print("📊 Interactive visualization functions defined!")

def compute_attention_statistics(attention_scheming, attention_baseline, input_tokens):
    """Compute comprehensive statistics comparing attention patterns."""
    results = {}

    # Ensure same length for comparison
    min_len = min(len(attention_scheming), len(attention_baseline))
    att_s = attention_scheming[:min_len]
    att_b = attention_baseline[:min_len]

    # Basic statistics
    results['mean_attention_scheming'] = np.mean(att_s)
    results['mean_attention_baseline'] = np.mean(att_b)
    results['std_attention_scheming'] = np.std(att_s)
    results['std_attention_baseline'] = np.std(att_b)

    # Statistical tests
    t_stat, p_value = stats.ttest_rel(att_s, att_b)
    results['t_statistic'] = t_stat
    results['p_value'] = p_value
    results['significant'] = p_value < 0.05

    # Effect size (Cohen's d)
    pooled_std = np.sqrt((np.var(att_s) + np.var(att_b)) / 2)
    results['cohens_d'] = (np.mean(att_s) - np.mean(att_b)) / pooled_std if pooled_std > 0 else 0

    # Cosine similarity
    if len(att_s) > 0 and len(att_b) > 0:
        results['cosine_similarity'] = cosine_similarity([att_s], [att_b])[0, 0]
    else:
        results['cosine_similarity'] = 0

    # Top differing tokens
    attention_diff = np.array(att_s) - np.array(att_b)
    top_indices = np.argsort(np.abs(attention_diff))[-10:][::-1]

    results['top_differences'] = []
    for idx in top_indices:
        if idx < len(input_tokens):
            results['top_differences'].append({
                'token': input_tokens[idx],
                'position': int(idx),
                'scheming_attention': float(att_s[idx]),
                'baseline_attention': float(att_b[idx]),
                'difference': float(attention_diff[idx]),
                'abs_difference': float(abs(attention_diff[idx]))
            })

    return results

def analyze_token_importance_by_type(attention_weights, input_tokens):
    """Analyze attention patterns by token type."""
    token_types = identify_token_types(input_tokens)

    # Group attention by token type
    type_attention = {}
    for token, token_type, attention in zip(input_tokens, token_types, attention_weights):
        if token_type not in type_attention:
            type_attention[token_type] = []
        type_attention[token_type].append(attention)

    # Compute statistics for each type
    type_stats = {}
    for token_type, attentions in type_attention.items():
        type_stats[token_type] = {
            'mean': np.mean(attentions),
            'std': np.std(attentions),
            'count': len(attentions),
            'total_attention': np.sum(attentions)
        }

    return type_stats

def create_statistical_summary_plot(stats_results):
    """Create a comprehensive statistical summary visualization."""
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            "Mean Attention Comparison",
            "Top Token Differences",
            "Statistical Significance",
            "Effect Size & Similarity"
        ],
        specs=[[{"type": "bar"}, {"type": "bar"}],
               [{"type": "indicator"}, {"type": "bar"}]]
    )

    # Mean attention comparison
    fig.add_trace(
        go.Bar(
            x=["Scheming", "Baseline"],
            y=[stats_results['mean_attention_scheming'], stats_results['mean_attention_baseline']],
            error_y=dict(array=[stats_results['std_attention_scheming'], stats_results['std_attention_baseline']]),
            marker_color=['red', 'blue'],
            name="Mean Attention"
        ),
        row=1, col=1
    )

    # Top differences
    if stats_results['top_differences']:
        top_5 = stats_results['top_differences'][:5]
        fig.add_trace(
            go.Bar(
                x=[d['token'] for d in top_5],
                y=[d['difference'] for d in top_5],
                marker_color=['red' if d['difference'] > 0 else 'blue' for d in top_5],
                name="Attention Difference"
            ),
            row=1, col=2
        )

    # Statistical significance indicator
    significance_color = "green" if stats_results['significant'] else "red"
    fig.add_trace(
        go.Indicator(
            mode="gauge+number",
            value=-np.log10(stats_results['p_value']) if stats_results['p_value'] > 0 else 10,
            title={"text": "-log10(p-value)"},
            gauge={
                "axis": {"range": [0, 10]},
                "bar": {"color": significance_color},
                "steps": [{"range": [0, 1.3], "color": "lightgray"},
                         {"range": [1.3, 10], "color": "lightgreen"}],
                "threshold": {"line": {"color": "red", "width": 4},
                            "thickness": 0.75, "value": 1.3}
            }
        ),
        row=2, col=1
    )

    # Effect size and similarity
    fig.add_trace(
        go.Bar(
            x=["Cohen's d", "Cosine Similarity"],
            y=[stats_results['cohens_d'], stats_results['cosine_similarity']],
            marker_color=['orange', 'purple'],
            name="Effect Measures"
        ),
        row=2, col=2
    )

    fig.update_layout(
        title="Statistical Analysis Summary",
        showlegend=False,
        height=800,
        width=1200
    )

    return fig

print("📈 Statistical analysis functions defined!")

def analyze_example_advanced(example_idx, layers_to_analyze=None, num_tokens_to_generate=8):
    """Perform comprehensive attention analysis for a specific example."""
    if example_idx >= len(dataset):
        print(f"❌ Invalid example index. Dataset has {len(dataset)} examples.")
        return None

    example = dataset[example_idx]

    print(f"\n🔍 === Advanced Analysis: Example {example_idx} ===")
    print(f"📂 Pressure Category: {example.get('pressure_category', 'N/A')}")
    print(f"🎯 Pressure Source: {example.get('pressure_source', 'N/A')}")
    print(f"✅ Ground Truth: {example['ground_truth']}")
    print(f"😇 Honest Answer: {example['honest']}")
    print(f"😈 Dishonest Answer: {example['dishonest']}")

    # Prepare scenarios
    print("\n🔄 Preparing scenarios...")

    # Scenario A: Scheming (original prompts)
    inputs_a, prompt_a = prepare_inputs(
        example['system_prompt'],
        example['user_prompt']
    )

    # Scenario B: Baseline (neutral system + original context)
    baseline_system = "You are a helpful assistant."
    baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    inputs_b, prompt_b = prepare_inputs(baseline_system, baseline_user)

    print("\n📊 Extracting multi-layer attention patterns...")

    # Extract attention patterns
    layer_attentions_a, generated_a, log_probs_a, perplexity_a = extract_multi_layer_attention(
        inputs_a, num_tokens_to_generate, layers_to_analyze
    )

    layer_attentions_b, generated_b, log_probs_b, perplexity_b = extract_multi_layer_attention(
        inputs_b, num_tokens_to_generate, layers_to_analyze
    )

    # Get clean tokens
    tokens_a = get_readable_tokens(inputs_a['input_ids'][0], tokenizer)
    tokens_b = get_readable_tokens(inputs_b['input_ids'][0], tokenizer)

    print(f"\n📈 Perplexity - Scheming: {perplexity_a:.2f}, Baseline: {perplexity_b:.2f}")
    print(f"🎲 Generated tokens - Scheming: {len(generated_a)}, Baseline: {len(generated_b)}")

    # Perform statistical analysis on last layer
    last_layer = max(layer_attentions_a.keys())
    if layer_attentions_a[last_layer] and layer_attentions_b[last_layer]:
        # Average attention across generation steps - handle variable sequence lengths
        attention_arrays_a = [step['avg_attention'] for step in layer_attentions_a[last_layer]]
        attention_arrays_b = [step['avg_attention'] for step in layer_attentions_b[last_layer]]

        # Find the minimum length across all arrays to ensure consistent shapes
        min_seq_len = min([len(arr) for arr in attention_arrays_a + attention_arrays_b])

        # Truncate all arrays to the same length and compute mean
        truncated_a = [arr[:min_seq_len] for arr in attention_arrays_a]
        truncated_b = [arr[:min_seq_len] for arr in attention_arrays_b]

        avg_attention_a = np.mean(truncated_a, axis=0)
        avg_attention_b = np.mean(truncated_b, axis=0)

        # Ensure same length for comparison
        min_len = min(len(avg_attention_a), len(avg_attention_b), len(tokens_a), len(tokens_b))

        stats_results = compute_attention_statistics(
            avg_attention_a[:min_len],
            avg_attention_b[:min_len],
            tokens_a[:min_len]
        )

        print(f"\n📊 Statistical Analysis (Layer {last_layer}):")
        print(f"  Mean attention difference: {stats_results['mean_attention_scheming'] - stats_results['mean_attention_baseline']:.4f}")
        print(f"  Statistical significance: {'Yes' if stats_results['significant'] else 'No'} (p={stats_results['p_value']:.4f})")
        print(f"  Effect size (Cohen's d): {stats_results['cohens_d']:.4f}")
        print(f"  Pattern similarity: {stats_results['cosine_similarity']:.4f}")
    else:
        stats_results = None
        print("\n⚠️ Insufficient data for statistical analysis")

    # Create visualizations
    print("\n🎨 Creating advanced visualizations...")

    results = {
        'example_idx': example_idx,
        'example_data': example,
        'layer_attentions_scheming': layer_attentions_a,
        'layer_attentions_baseline': layer_attentions_b,
        'tokens_scheming': tokens_a,
        'tokens_baseline': tokens_b,
        'generated_scheming': generated_a,
        'generated_baseline': generated_b,
        'perplexity_scheming': perplexity_a,
        'perplexity_baseline': perplexity_b,
        'statistical_results': stats_results
    }

    return results

print("🚀 Main analysis function defined!")

# Create interactive controls
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset)-1, 50),  # Limit for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

category_dropdown = widgets.Dropdown(
    options=['All'] + sorted(list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:51]]))),
    value='All',
    description='Category:',
    style={'description_width': 'initial'}
)

# Create layer options based on model size
num_layers = model.config.num_hidden_layers
# For smaller models, show more layers; for larger models, focus on last layers
# if num_layers <= 12:
#     layer_start = max(0, num_layers - 8)
#     default_layers = tuple(range(max(0, num_layers - 4), num_layers))
# else:
#     layer_start = max(0, num_layers - 12)
#     default_layers = tuple(range(max(0, num_layers - 6), num_layers))

# Always show all layers
layer_start = 0
default_layers = tuple(range(num_layers))

layers_multiselect = widgets.SelectMultiple(
    options=[(f'Layer {i}', i) for i in range(layer_start, num_layers)],
    value=default_layers,
    description='Layers:',
    style={'description_width': 'initial'}
)

print(f"📊 Model has {num_layers} layers. Showing layers {layer_start}-{num_layers-1}, default: {list(default_layers)}")

tokens_slider = widgets.IntSlider(
    value=8,
    min=3,
    max=15,
    step=1,
    description='Gen Tokens:',
    style={'description_width': 'initial'}
)

analysis_button = widgets.Button(
    description='🔍 Run Advanced Analysis',
    button_style='primary',
    layout=widgets.Layout(width='200px', height='40px')
)

output_area = widgets.Output()

def on_analysis_click(b):
    with output_area:
        output_area.clear_output()

        # Run analysis
        results = analyze_example_advanced(
            example_slider.value,
            list(layers_multiselect.value),
            tokens_slider.value
        )

        if results is None:
            return

        # Display visualizations
        print("\n🎨 Generating interactive visualizations...")

        # 1. Multi-layer comparison
        if results['layer_attentions_scheming'] and results['layer_attentions_baseline']:
            fig1 = create_multi_layer_comparison(
                results['layer_attentions_scheming'],
                results['layer_attentions_baseline'],
                results['tokens_scheming']
            )
            fig1.show()

        # 2. Statistical summary
        if results['statistical_results']:
            fig2 = create_statistical_summary_plot(results['statistical_results'])
            fig2.show()

            # Print top differences
            print("\n🔝 Top Attention Differences:")
            for i, diff in enumerate(results['statistical_results']['top_differences'][:5]):
                direction = "📈 MORE" if diff['difference'] > 0 else "📉 LESS"
                print(f"  {i+1}. '{diff['token']}' (pos {diff['position']}): {direction} attention in scheming ({diff['difference']:.4f})")

        # 3. Attention head analysis for last layer
        last_layer = max(results['layer_attentions_scheming'].keys())
        fig3 = create_attention_head_analysis(
            results['layer_attentions_scheming'],
            results['tokens_scheming'],
            last_layer
        )
        if fig3:
            fig3.show()

analysis_button.on_click(on_analysis_click)

# Display controls
controls = widgets.VBox([
    widgets.HBox([category_dropdown, example_slider]),
    widgets.HBox([layers_multiselect, tokens_slider]),
    analysis_button
])

display(controls)
display(output_area)