import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from IPython.display import display, HTML
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual
import warnings
from bertviz import head_view, model_view
import re

warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("📚 Libraries loaded successfully!")
print("🎨 BertViz visualization tools ready!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "meta-llama/Llama-3.1-8B"

# Load model and tokenizer with optimized settings
print(f"🤖 Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None,
    use_cache=False  # Disable cache for attention analysis
)

# Configure tokenizer
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"🔧 Model loaded on device: {device}")
print(f"📊 Model architecture: {model.config.num_hidden_layers} layers, {model.config.num_attention_heads} heads")
print(f"🧠 Memory usage: {torch.cuda.memory_allocated()/1e9:.2f}GB" if torch.cuda.is_available() else "CPU mode")

def clean_llama_tokens(tokens):
    """Clean up Llama tokenizer tokens by removing the 'Ġ' character that represents spaces."""
    cleaned_tokens = []
    for token in tokens:
        if token.startswith('Ġ'):
            # Replace 'Ġ' with space and strip to avoid leading spaces
            cleaned_token = token.replace('Ġ', ' ').strip()
            # If the token becomes empty after cleaning, use the original
            cleaned_tokens.append(cleaned_token if cleaned_token else token)
        else:
            cleaned_tokens.append(token)
    return cleaned_tokens

def get_readable_tokens(input_ids, tokenizer):
    """Convert token IDs to readable tokens with proper space handling."""
    raw_tokens = tokenizer.convert_ids_to_tokens(input_ids)
    return clean_llama_tokens(raw_tokens)

print("🔧 Token processing utilities defined!")

# Test the token cleaning
test_text = "Hello world! This is a test."
test_tokens = tokenizer.tokenize(test_text)
cleaned_tokens = clean_llama_tokens(test_tokens)
print(f"\n🧪 Token cleaning test:")
print(f"Raw tokens: {test_tokens[:5]}...")
print(f"Cleaned tokens: {cleaned_tokens[:5]}...")

def load_dataset(path):
    """Load and validate dataset."""
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data.append(json.loads(line.strip()))
            except json.JSONDecodeError as e:
                print(f"⚠️ Warning: Invalid JSON on line {line_num}: {e}")
                continue
    return data

# Load and analyze dataset
dataset = load_dataset(DATASET_PATH)
print(f"📁 Loaded {len(dataset)} examples from dataset")

# Analyze dataset composition
categories = [ex.get('pressure_category', 'unknown') for ex in dataset]
category_counts = pd.Series(categories).value_counts()
print(f"\n📊 Dataset composition:")
for cat, count in category_counts.items():
    print(f"  {cat}: {count} examples ({count/len(dataset)*100:.1f}%)")

def prepare_inputs(system_prompt, user_prompt, max_length=512):
    """Prepare inputs for the model using chat template."""
    # Create conversation in chat format
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    # Apply chat template
    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:
        formatted_prompt = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
    else:
        # Fallback to manual formatting if no chat template
        formatted_prompt = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"

    # Tokenize
    inputs = tokenizer(
        formatted_prompt,
        return_tensors="pt",
        max_length=max_length,
        truncation=True,
        padding=True
    )

    return inputs, formatted_prompt

def extract_attention_for_bertviz(inputs, num_tokens_to_generate=5):
    """Extract attention patterns for BertViz visualization."""
    model.eval()

    with torch.no_grad():
        # Move inputs to device
        input_ids = inputs['input_ids'].to(device)
        attention_mask = inputs['attention_mask'].to(device)

        # Get attention for the input sequence
        outputs = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            output_attentions=True
        )

        # Extract attention weights from all layers
        # Shape: [num_layers, batch_size, num_heads, seq_len, seq_len]
        all_attentions = outputs.attentions

        # Generate a few tokens to get generation attention
        generated_tokens = []
        token_log_probs = []
        generation_attentions = []

        current_input_ids = input_ids
        current_attention_mask = attention_mask

        for step in range(num_tokens_to_generate):
            # Forward pass
            outputs = model(
                input_ids=current_input_ids,
                attention_mask=current_attention_mask,
                output_attentions=True
            )

            # Get next token probabilities
            logits = outputs.logits[0, -1, :]
            log_probs = torch.log_softmax(logits, dim=-1)
            next_token_id = torch.argmax(logits, dim=-1).unsqueeze(0).unsqueeze(0)

            # Store log probability of selected token
            token_log_prob = log_probs[next_token_id.item()].item()
            token_log_probs.append(token_log_prob)

            # Store generated token
            generated_tokens.append(next_token_id.item())

            # Store attention from this generation step
            generation_attentions.append(outputs.attentions)

            # Update inputs for next iteration
            current_input_ids = torch.cat([current_input_ids, next_token_id], dim=1)
            current_attention_mask = torch.cat([
                current_attention_mask,
                torch.ones(1, 1, device=device)
            ], dim=1)

            # Stop if EOS token
            if next_token_id.item() == tokenizer.eos_token_id:
                break

    # Calculate perplexity
    if token_log_probs:
        avg_log_prob = np.mean(token_log_probs)
        perplexity = np.exp(-avg_log_prob)
    else:
        perplexity = float('inf')

    return {
        'input_attentions': all_attentions,
        'generation_attentions': generation_attentions,
        'generated_tokens': generated_tokens,
        'token_log_probs': token_log_probs,
        'perplexity': perplexity,
        'final_input_ids': current_input_ids
    }

print("🔬 Attention extraction functions defined!")

def create_bertviz_head_view(attention_weights, tokens, title, layer_idx=-1):
    """Create BertViz head view visualization for a specific layer."""
    try:
        # Select specific layer attention
        if isinstance(attention_weights, (list, tuple)):
            layer_attention = attention_weights[layer_idx]  # Shape: [batch, heads, seq_len, seq_len]
        else:
            layer_attention = attention_weights

        # Ensure tensor is on CPU
        if isinstance(layer_attention, torch.Tensor):
            layer_attention = layer_attention.cpu()

        # Clean tokens for display
        clean_tokens = clean_llama_tokens(tokens)

        print(f"\n🎨 {title} - Layer {layer_idx}")
        print(f"📊 Attention shape: {layer_attention.shape}")
        print(f"🔤 Tokens: {len(clean_tokens)}")

        # Create head view
        html = head_view(
            attention=layer_attention,
            tokens=clean_tokens,
            html_action='return'
        )

        # Display the HTML
        display(HTML(html.data))

        return html

    except Exception as e:
        print(f"❌ Error creating head view: {e}")
        print(f"   Attention shape: {layer_attention.shape if hasattr(layer_attention, 'shape') else 'unknown'}")
        print(f"   Tokens length: {len(tokens)}")
        return None

def create_bertviz_model_view(attention_weights, tokens, title, layers_to_show=None):
    """Create BertViz model view showing multiple layers."""
    try:
        # Stack attention weights from multiple layers
        if layers_to_show is None:
            # Show last 6 layers by default
            layers_to_show = list(range(len(attention_weights)-6, len(attention_weights)))

        # Select and stack specified layers
        selected_attentions = [attention_weights[i] for i in layers_to_show if i < len(attention_weights)]

        # Stack along layer dimension: [batch, layers, heads, seq_len, seq_len]
        stacked_attention = torch.stack(selected_attentions, dim=1)

        # Ensure tensor is on CPU
        if isinstance(stacked_attention, torch.Tensor):
            stacked_attention = stacked_attention.cpu()

        # Clean tokens for display
        clean_tokens = clean_llama_tokens(tokens)

        print(f"\n🎨 {title} - Layers {layers_to_show}")
        print(f"📊 Attention shape: {stacked_attention.shape}")
        print(f"🔤 Tokens: {len(clean_tokens)}")

        # Create model view
        html = model_view(
            attention=stacked_attention,
            tokens=clean_tokens,
            html_action='return'
        )

        # Display the HTML
        display(HTML(html.data))

        return html

    except Exception as e:
        print(f"❌ Error creating model view: {e}")
        print(f"   Attention shape: {stacked_attention.shape if 'stacked_attention' in locals() else 'unknown'}")
        print(f"   Layers to show: {layers_to_show}")
        return None

def create_perplexity_comparison(perplexity_scheming, perplexity_baseline,
                               token_probs_scheming, token_probs_baseline,
                               generated_tokens_scheming, generated_tokens_baseline):
    """Create perplexity comparison visualization."""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Overall perplexity comparison
    scenarios = ['Scheming', 'Baseline']
    perplexities = [perplexity_scheming, perplexity_baseline]
    colors = ['red', 'blue']

    bars = ax1.bar(scenarios, perplexities, color=colors, alpha=0.7)
    ax1.set_ylabel('Perplexity')
    ax1.set_title('Overall Perplexity Comparison')
    ax1.grid(True, alpha=0.3)

    # Add value labels on bars
    for bar, perp in zip(bars, perplexities):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{perp:.2f}', ha='center', va='bottom', fontweight='bold')

    # Token-level log probabilities
    max_len = min(len(token_probs_scheming), len(token_probs_baseline), 10)  # Show first 10 tokens

    if max_len > 0:
        x_pos = np.arange(max_len)
        width = 0.35

        ax2.bar(x_pos - width/2, token_probs_scheming[:max_len], width,
               label='Scheming', color='red', alpha=0.7)
        ax2.bar(x_pos + width/2, token_probs_baseline[:max_len], width,
               label='Baseline', color='blue', alpha=0.7)

        # Set token labels
        token_labels = []
        for i in range(max_len):
            if i < len(generated_tokens_scheming):
                token = tokenizer.decode([generated_tokens_scheming[i]])
                token_labels.append(token[:8] + '...' if len(token) > 8 else token)
            else:
                token_labels.append(f'T{i}')

        ax2.set_xlabel('Generated Tokens')
        ax2.set_ylabel('Log Probability')
        ax2.set_title('Token-level Log Probabilities')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(token_labels, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # Print summary statistics
    print(f"\n📊 Perplexity Analysis:")
    print(f"  Scheming Perplexity: {perplexity_scheming:.3f}")
    print(f"  Baseline Perplexity: {perplexity_baseline:.3f}")
    print(f"  Difference: {perplexity_scheming - perplexity_baseline:.3f}")
    print(f"  Ratio: {perplexity_scheming / perplexity_baseline:.3f}")

    if token_probs_scheming and token_probs_baseline:
        avg_logprob_scheming = np.mean(token_probs_scheming)
        avg_logprob_baseline = np.mean(token_probs_baseline)
        print(f"  Average Log Prob (Scheming): {avg_logprob_scheming:.3f}")
        print(f"  Average Log Prob (Baseline): {avg_logprob_baseline:.3f}")

print("🎨 BertViz visualization functions defined!")

def analyze_example_bertviz(example_idx, num_tokens_to_generate=5, layer_to_show=-1):
    """Perform BertViz attention analysis for a specific example."""
    if example_idx >= len(dataset):
        print(f"❌ Invalid example index. Dataset has {len(dataset)} examples.")
        return None

    example = dataset[example_idx]

    print(f"\n🔍 === BertViz Analysis: Example {example_idx} ===")
    print(f"📂 Pressure Category: {example.get('pressure_category', 'N/A')}")
    print(f"🎯 Pressure Source: {example.get('pressure_source', 'N/A')}")
    print(f"✅ Ground Truth: {example['ground_truth']}")
    print(f"😇 Honest Answer: {example['honest']}")
    print(f"😈 Dishonest Answer: {example['dishonest']}")

    # Prepare scenarios
    print("\n🔄 Preparing scenarios...")

    # Scenario A: Scheming (original prompts)
    inputs_a, prompt_a = prepare_inputs(
        example['system_prompt'],
        example['user_prompt']
    )

    # Scenario B: Baseline (neutral system + original context)
    baseline_system = "You are a helpful assistant."
    baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    inputs_b, prompt_b = prepare_inputs(baseline_system, baseline_user)

    print("\n📊 Extracting attention patterns...")

    # Extract attention patterns
    results_a = extract_attention_for_bertviz(inputs_a, num_tokens_to_generate)
    results_b = extract_attention_for_bertviz(inputs_b, num_tokens_to_generate)

    # Get clean tokens
    tokens_a = get_readable_tokens(inputs_a['input_ids'][0], tokenizer)
    tokens_b = get_readable_tokens(inputs_b['input_ids'][0], tokenizer)

    print(f"\n📈 Perplexity - Scheming: {results_a['perplexity']:.2f}, Baseline: {results_b['perplexity']:.2f}")
    print(f"🎲 Generated tokens - Scheming: {len(results_a['generated_tokens'])}, Baseline: {len(results_b['generated_tokens'])}")

    # Create visualizations
    print("\n🎨 Creating BertViz visualizations...")

    # 1. Perplexity comparison
    create_perplexity_comparison(
        results_a['perplexity'], results_b['perplexity'],
        results_a['token_log_probs'], results_b['token_log_probs'],
        results_a['generated_tokens'], results_b['generated_tokens']
    )

    # 2. Head view for scheming scenario
    print("\n🔍 Scheming Scenario - Head View:")
    create_bertviz_head_view(
        results_a['input_attentions'],
        tokens_a,
        "Scheming Scenario",
        layer_to_show
    )

    # 3. Head view for baseline scenario
    print("\n🔍 Baseline Scenario - Head View:")
    create_bertviz_head_view(
        results_b['input_attentions'],
        tokens_b,
        "Baseline Scenario",
        layer_to_show
    )

    # 4. Model view for scheming scenario (multiple layers)
    print("\n🏗️ Scheming Scenario - Model View:")
    create_bertviz_model_view(
        results_a['input_attentions'],
        tokens_a,
        "Scheming Scenario - Multi-Layer"
    )

    # 5. Model view for baseline scenario (multiple layers)
    print("\n🏗️ Baseline Scenario - Model View:")
    create_bertviz_model_view(
        results_b['input_attentions'],
        tokens_b,
        "Baseline Scenario - Multi-Layer"
    )

    return {
        'example_idx': example_idx,
        'example_data': example,
        'scheming_results': results_a,
        'baseline_results': results_b,
        'tokens_scheming': tokens_a,
        'tokens_baseline': tokens_b
    }

print("🚀 Main analysis function defined!")

# Create interactive controls
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset)-1, 20),  # Limit for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

category_dropdown = widgets.Dropdown(
    options=['All'] + sorted(list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:21]]))),
    value='All',
    description='Category:',
    style={'description_width': 'initial'}
)

layer_slider = widgets.IntSlider(
    value=-1,  # -1 means last layer
    min=-6,
    max=-1,
    step=1,
    description='Layer (from end):',
    style={'description_width': 'initial'}
)

tokens_slider = widgets.IntSlider(
    value=5,
    min=3,
    max=10,
    step=1,
    description='Gen Tokens:',
    style={'description_width': 'initial'}
)

analysis_button = widgets.Button(
    description='🔍 Run BertViz Analysis',
    button_style='primary',
    layout=widgets.Layout(width='200px', height='40px')
)

output_area = widgets.Output()

def on_analysis_click(b):
    with output_area:
        output_area.clear_output()

        # Run analysis
        results = analyze_example_bertviz(
            example_slider.value,
            tokens_slider.value,
            layer_slider.value
        )

        if results is None:
            return

        print("\n✅ Analysis complete! Scroll up to see the visualizations.")

analysis_button.on_click(on_analysis_click)

# Display controls
controls = widgets.VBox([
    widgets.HBox([category_dropdown, example_slider]),
    widgets.HBox([layer_slider, tokens_slider]),
    analysis_button
])

display(controls)
display(output_area)

print("\n🎛️ Interactive controls ready!")
print("📋 Instructions:")
print("  1. Select a pressure category (optional filter)")
print("  2. Choose an example number")
print("  3. Select which layer to focus on (negative numbers count from end)")
print("  4. Set number of tokens to generate")
print("  5. Click 'Run BertViz Analysis' to start")
print("\n✨ Features:")
print("  • BertViz Head View: Interactive attention head visualization")
print("  • BertViz Model View: Multi-layer attention patterns")
print("  • Perplexity Analysis: Token-level and overall comparison")
print("  • Clean token display (no more 'Ġ' characters!)")
print("  • Side-by-side scheming vs baseline comparison")

