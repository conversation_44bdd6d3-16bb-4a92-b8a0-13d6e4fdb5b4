{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BertViz Attention Visualization for Scheming Dataset (Fixed)\n", "\n", "This notebook uses BertViz to create interactive attention visualizations comparing scheming vs. baseline scenarios.\n", "\n", "## Key Features:\n", "- **<PERSON><PERSON><PERSON> Head View**: Interactive attention head visualization\n", "- **BertViz Model View**: Multi-layer attention patterns\n", "- **Full Response Generation**: Complete model responses (not just a few tokens)\n", "- **Per-Token Perplexity**: Individual token confidence analysis\n", "- **All Layers Support**: Option to visualize all transformer layers\n", "- **Clean Token Display**: <PERSON><PERSON><PERSON> handles Llama tokenizer's 'Ġ' characters\n", "\n", "## Analysis Goals:\n", "- **Scenario A (<PERSON><PERSON><PERSON>)**: Pressure-inducing system prompts\n", "- **Scenario B (Baseline)**: Neutral system prompts\n", "- **Visual Comparison**: Interactive attention pattern exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas as pd\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import torch\n", "from IPython.display import display, HTML\n", "import ipywidgets as widgets\n", "from ipywidgets import interact, interactive, fixed, interact_manual\n", "import warnings\n", "from bertviz import head_view, model_view\n", "import re\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (15, 8)\n", "plt.rcParams['font.size'] = 10\n", "\n", "print(\"📚 Libraries loaded successfully!\")\n", "print(\"🎨 BertViz visualization tools ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration and Model Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "DATASET_PATH = \"../../dataset_creation/squad_scheming_dataset.jsonl\"\n", "MODEL_NAME = \"meta-llama/Llama-3.1-8B\"\n", "\n", "# Load model and tokenizer with optimized settings\n", "print(f\"🤖 Loading model: {MODEL_NAME}\")\n", "tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    MODEL_NAME,\n", "    output_attentions=True,\n", "    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,\n", "    device_map=\"auto\" if torch.cuda.is_available() else None,\n", "    use_cache=False  # Disable cache for attention analysis\n", ")\n", "\n", "# Configure tokenizer\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "device = next(model.parameters()).device\n", "print(f\"🔧 Model loaded on device: {device}\")\n", "print(f\"📊 Model architecture: {model.config.num_hidden_layers} layers, {model.config.num_attention_heads} heads\")\n", "print(f\"🧠 Memory usage: {torch.cuda.memory_allocated()/1e9:.2f}GB\" if torch.cuda.is_available() else \"CPU mode\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Token Processing Utilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def clean_llama_tokens(tokens):\n", "    \"\"\"Clean up Llama tokenizer tokens by removing the 'Ġ' character that represents spaces.\"\"\"\n", "    cleaned_tokens = []\n", "    for token in tokens:\n", "        if token.startswith('Ġ'):\n", "            # Replace 'Ġ' with space and strip to avoid leading spaces\n", "            cleaned_token = token.replace('Ġ', ' ').strip()\n", "            # If the token becomes empty after cleaning, use the original\n", "            cleaned_tokens.append(cleaned_token if cleaned_token else token)\n", "        else:\n", "            cleaned_tokens.append(token)\n", "    return cleaned_tokens\n", "\n", "def get_readable_tokens(input_ids, tokenizer):\n", "    \"\"\"Convert token IDs to readable tokens with proper space handling.\"\"\"\n", "    raw_tokens = tokenizer.convert_ids_to_tokens(input_ids)\n", "    return clean_llama_tokens(raw_tokens)\n", "\n", "print(\"🔧 Token processing utilities defined!\")\n", "\n", "# Test the token cleaning\n", "test_text = \"Hello world! This is a test.\"\n", "test_tokens = tokenizer.tokenize(test_text)\n", "cleaned_tokens = clean_llama_tokens(test_tokens)\n", "print(f\"\\n🧪 Token cleaning test:\")\n", "print(f\"Raw tokens: {test_tokens[:5]}...\")\n", "print(f\"Cleaned tokens: {cleaned_tokens[:5]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_dataset(path):\n", "    \"\"\"Load and validate dataset.\"\"\"\n", "    data = []\n", "    with open(path, 'r', encoding='utf-8') as f:\n", "        for line_num, line in enumerate(f, 1):\n", "            try:\n", "                data.append(json.loads(line.strip()))\n", "            except json.JSONDecodeError as e:\n", "                print(f\"⚠️ Warning: Invalid JSON on line {line_num}: {e}\")\n", "                continue\n", "    return data\n", "\n", "# Load and analyze dataset\n", "dataset = load_dataset(DATASET_PATH)\n", "print(f\"📁 Loaded {len(dataset)} examples from dataset\")\n", "\n", "# Analyze dataset composition\n", "categories = [ex.get('pressure_category', 'unknown') for ex in dataset]\n", "category_counts = pd.Series(categories).value_counts()\n", "print(f\"\\n📊 Dataset composition:\")\n", "for cat, count in category_counts.items():\n", "    print(f\"  {cat}: {count} examples ({count/len(dataset)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Attention Extraction Functions (Fixed)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_inputs(system_prompt, user_prompt, max_length=512):\n", "    \"\"\"Prepare inputs for the model using chat template.\"\"\"\n", "    # Create conversation in chat format\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ]\n", "\n", "    # Apply chat template\n", "    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:\n", "        formatted_prompt = tokenizer.apply_chat_template(\n", "            messages,\n", "            tokenize=False,\n", "            add_generation_prompt=True\n", "        )\n", "    else:\n", "        # Fallback to manual formatting if no chat template\n", "        formatted_prompt = f\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\"\n", "\n", "    # Tokenize\n", "    inputs = tokenizer(\n", "        formatted_prompt,\n", "        return_tensors=\"pt\",\n", "        max_length=max_length,\n", "        truncation=True,\n", "        padding=True\n", "    )\n", "\n", "    return inputs, formatted_prompt\n", "\n", "def extract_attention_for_bertviz(inputs, max_new_tokens=50):\n", "    \"\"\"Extract attention patterns for <PERSON><PERSON><PERSON> visualization with full response generation.\"\"\"\n", "    model.eval()\n", "\n", "    with torch.no_grad():\n", "        # Move inputs to device\n", "        input_ids = inputs['input_ids'].to(device)\n", "        attention_mask = inputs['attention_mask'].to(device)\n", "\n", "        # Generate full response using model.generate()\n", "        generation_outputs = model.generate(\n", "            input_ids=input_ids,\n", "            attention_mask=attention_mask,\n", "            max_new_tokens=max_new_tokens,\n", "            do_sample=False,  # Use greedy decoding for consistency\n", "            output_attentions=True,\n", "            return_dict_in_generate=True,\n", "            pad_token_id=tokenizer.eos_token_id\n", "        )\n", "\n", "        # Extract generated sequence and attention\n", "        generated_sequence = generation_outputs.sequences[0]\n", "        generated_tokens = generated_sequence[input_ids.shape[1]:].tolist()  # Only new tokens\n", "\n", "        # Get attention from the final forward pass (input + all generated tokens)\n", "        final_outputs = model(\n", "            input_ids=generated_sequence.unsqueeze(0),\n", "            output_attentions=True\n", "        )\n", "\n", "        # Calculate per-token perplexity for generated tokens\n", "        token_log_probs = []\n", "        token_perplexities = []\n", "\n", "        if len(generated_tokens) > 0:\n", "            # Get logits for the generated portion\n", "            logits = final_outputs.logits[0, input_ids.shape[1]-1:-1, :]  # Exclude last position\n", "\n", "            for i, token_id in enumerate(generated_tokens):\n", "                if i < logits.shape[0]:\n", "                    log_probs = torch.log_softmax(logits[i], dim=-1)\n", "                    token_log_prob = log_probs[token_id].item()\n", "                    token_log_probs.append(token_log_prob)\n", "                    token_perplexities.append(np.exp(-token_log_prob))\n", "\n", "        # Calculate overall perplexity\n", "        if token_log_probs:\n", "            avg_log_prob = np.mean(token_log_probs)\n", "            overall_perplexity = np.exp(-avg_log_prob)\n", "        else:\n", "            overall_perplexity = float('inf')\n", "\n", "        # Get the generated text (only the assistant response)\n", "        generated_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)\n", "\n", "        # Get the full conversation text\n", "        full_conversation = tokenizer.decode(generated_sequence, skip_special_tokens=True)\n", "\n", "    return {\n", "        'input_attentions': final_outputs.attentions,\n", "        'generated_tokens': generated_tokens,\n", "        'generated_text': generated_text,\n", "        'full_conversation': full_conversation,\n", "        'token_log_probs': token_log_probs,\n", "        'token_perplexities': token_perplexities,\n", "        'overall_perplexity': overall_perplexity,\n", "        'final_input_ids': generated_sequence,\n", "        'input_length': input_ids.shape[1]\n", "    }\n", "\n", "print(\"🔬 Fixed attention extraction functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## BertViz Visualization Functions (Fixed)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_bertviz_head_view(attention_weights, tokens, title, layer_idx=-1):\n", "    \"\"\"Create Bert<PERSON><PERSON> head view visualization for a specific layer (FIXED).\"\"\"\n", "    try:\n", "        # Select specific layer attention\n", "        if isinstance(attention_weights, (list, tuple)):\n", "            layer_attention = attention_weights[layer_idx]  # Shape: [batch, heads, seq_len, seq_len]\n", "        else:\n", "            layer_attention = attention_weights\n", "\n", "        # Ensure tensor is on CPU and has correct shape for BertViz\n", "        if isinstance(layer_attention, torch.Tensor):\n", "            layer_attention = layer_attention.cpu()\n", "\n", "        # BertViz expects shape [batch, heads, seq_len, seq_len]\n", "        # Current shape is [batch, heads, seq_len, seq_len] - this is correct!\n", "\n", "        # Clean tokens for display\n", "        clean_tokens = clean_llama_tokens(tokens)\n", "\n", "        print(f\"\\n🎨 {title} - Layer {layer_idx}\")\n", "        print(f\"📊 Attention shape: {layer_attention.shape}\")\n", "        print(f\"🔤 Tokens: {len(clean_tokens)}\")\n", "\n", "        # Create head view - <PERSON><PERSON><PERSON> expects the attention tensor to be the right shape\n", "        html = head_view(\n", "            attention=layer_attention,\n", "            tokens=clean_tokens,\n", "            html_action='return'\n", "        )\n", "\n", "        # Display the HTML\n", "        display(HTML(html.data))\n", "\n", "        return html\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error creating head view: {e}\")\n", "        print(f\"   Attention shape: {layer_attention.shape if 'layer_attention' in locals() else 'unknown'}\")\n", "        print(f\"   Tokens length: {len(tokens)}\")\n", "        print(f\"   Layer index: {layer_idx}\")\n", "        return None\n", "\n", "def create_bertviz_model_view(attention_weights, tokens, title):\n", "    \"\"\"Create BertViz model view showing multiple layers (FIXED).\"\"\"\n", "    try:\n", "        # Always show all layers\n", "        layers_to_show = list(range(len(attention_weights)))\n", "\n", "        # Select and stack specified layers\n", "        selected_attentions = []\n", "        for i in layers_to_show:\n", "            if i < len(attention_weights):\n", "                selected_attentions.append(attention_weights[i])\n", "\n", "        if not selected_attentions:\n", "            print(f\"❌ No valid layers found. Available layers: 0-{len(attention_weights)-1}\")\n", "            return None\n", "\n", "        # Stack along layer dimension: [batch, layers, heads, seq_len, seq_len]\n", "        stacked_attention = torch.stack(selected_attentions, dim=1)\n", "\n", "        # Ensure tensor is on CPU\n", "        if isinstance(stacked_attention, torch.Tensor):\n", "            stacked_attention = stacked_attention.cpu()\n", "\n", "        # Clean tokens for display\n", "        clean_tokens = clean_llama_tokens(tokens)\n", "\n", "        print(f\"\\n🎨 {title} - Layers {layers_to_show[:5]}{'...' if len(layers_to_show) > 5 else ''}\")\n", "        print(f\"📊 Attention shape: {stacked_attention.shape}\")\n", "        print(f\"🔤 Tokens: {len(clean_tokens)}\")\n", "\n", "        # Create model view\n", "        html = model_view(\n", "            attention=stacked_attention,\n", "            tokens=clean_tokens,\n", "            html_action='return'\n", "        )\n", "\n", "        # Display the HTML\n", "        display(HTML(html.data))\n", "\n", "        return html\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error creating model view: {e}\")\n", "        print(f\"   Attention shape: {stacked_attention.shape if 'stacked_attention' in locals() else 'unknown'}\")\n", "        print(f\"   Layers to show: {layers_to_show}\")\n", "        print(f\"   Available layers: 0-{len(attention_weights)-1}\")\n", "        return None\n", "\n", "print(\"🎨 Fixed BertViz visualization functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Enhanced Perplexity Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_enhanced_perplexity_comparison(results_scheming, results_baseline):\n", "    \"\"\"Create enhanced perplexity comparison with per-token analysis.\"\"\"\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))\n", "\n", "    # 1. Overall perplexity comparison\n", "    scenarios = ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>']\n", "    perplexities = [results_scheming['overall_perplexity'], results_baseline['overall_perplexity']]\n", "    colors = ['red', 'blue']\n", "\n", "    bars = ax1.bar(scenarios, perplexities, color=colors, alpha=0.7)\n", "    ax1.set_ylabel('Perplexity')\n", "    ax1.set_title('Overall Perplexity Comparison')\n", "    ax1.grid(True, alpha=0.3)\n", "\n", "    # Add value labels on bars\n", "    for bar, perp in zip(bars, perplexities):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{perp:.2f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "    # 2. Per-token perplexity comparison\n", "    max_len = min(len(results_scheming['token_perplexities']),\n", "                  len(results_baseline['token_perplexities']), 15)  # Show first 15 tokens\n", "\n", "    if max_len > 0:\n", "        x_pos = np.arange(max_len)\n", "        width = 0.35\n", "\n", "        ax2.bar(x_pos - width/2, results_scheming['token_perplexities'][:max_len], width,\n", "               label='Scheming', color='red', alpha=0.7)\n", "        ax2.bar(x_pos + width/2, results_baseline['token_perplexities'][:max_len], width,\n", "               label='Baseline', color='blue', alpha=0.7)\n", "\n", "        # Set token labels\n", "        token_labels = []\n", "        for i in range(max_len):\n", "            if i < len(results_scheming['generated_tokens']):\n", "                token = tokenizer.decode([results_scheming['generated_tokens'][i]])\n", "                token_labels.append(token[:8] + '...' if len(token) > 8 else token)\n", "            else:\n", "                token_labels.append(f'T{i}')\n", "\n", "        ax2.set_xlabel('Generated Tokens')\n", "        ax2.set_ylabel('Per-Token Perplexity')\n", "        ax2.set_title('Per-Token Perplexity Comparison')\n", "        ax2.set_xticks(x_pos)\n", "        ax2.set_xticklabels(token_labels, rotation=45, ha='right')\n", "        ax2.legend()\n", "        ax2.grid(True, alpha=0.3)\n", "\n", "    # 3. Token-level log probabilities\n", "    if max_len > 0:\n", "        ax3.bar(x_pos - width/2, results_scheming['token_log_probs'][:max_len], width,\n", "               label='Scheming', color='red', alpha=0.7)\n", "        ax3.bar(x_pos + width/2, results_baseline['token_log_probs'][:max_len], width,\n", "               label='Baseline', color='blue', alpha=0.7)\n", "\n", "        ax3.set_xlabel('Generated Tokens')\n", "        ax3.set_ylabel('Log Probability')\n", "        ax3.set_title('Token-level Log Probabilities')\n", "        ax3.set_xticks(x_pos)\n", "        ax3.set_xticklabels(token_labels, rotation=45, ha='right')\n", "        ax3.legend()\n", "        ax3.grid(True, alpha=0.3)\n", "\n", "    # 4. Generated text comparison\n", "    ax4.axis('off')\n", "    ax4.text(0.05, 0.9, 'Generated Responses:', fontsize=14, fontweight='bold', transform=ax4.transAxes)\n", "\n", "    # Show the actual generated responses (not truncated)\n", "    scheming_response = results_scheming['generated_text']\n", "    baseline_response = results_baseline['generated_text']\n", "\n", "    # Truncate only for display if very long\n", "    scheming_text = scheming_response[:300] + '...' if len(scheming_response) > 300 else scheming_response\n", "    baseline_text = baseline_response[:300] + '...' if len(baseline_response) > 300 else baseline_response\n", "\n", "    ax4.text(0.05, 0.7, f'Scheming ({results_scheming[\"overall_perplexity\"]:.2f} ppl):',\n", "             fontsize=12, fontweight='bold', color='red', transform=ax4.transAxes)\n", "    ax4.text(0.05, 0.6, scheming_text, fontsize=10, wrap=True, transform=ax4.transAxes)\n", "\n", "    ax4.text(0.05, 0.4, f'Baseline ({results_baseline[\"overall_perplexity\"]:.2f} ppl):',\n", "             fontsize=12, fontweight='bold', color='blue', transform=ax4.transAxes)\n", "    ax4.text(0.05, 0.3, baseline_text, fontsize=10, wrap=True, transform=ax4.transAxes)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # Print detailed summary statistics\n", "    print(f\"\\n📊 Detailed Perplexity Analysis:\")\n", "    print(f\"  Scheming Overall Perplexity: {results_scheming['overall_perplexity']:.3f}\")\n", "    print(f\"  Baseline Overall Perplexity: {results_baseline['overall_perplexity']:.3f}\")\n", "    print(f\"  Difference: {results_scheming['overall_perplexity'] - results_baseline['overall_perplexity']:.3f}\")\n", "    print(f\"  Ratio: {results_scheming['overall_perplexity'] / results_baseline['overall_perplexity']:.3f}\")\n", "\n", "    if results_scheming['token_log_probs'] and results_baseline['token_log_probs']:\n", "        avg_logprob_scheming = np.mean(results_scheming['token_log_probs'])\n", "        avg_logprob_baseline = np.mean(results_baseline['token_log_probs'])\n", "        print(f\"  Average Log Prob (Scheming): {avg_logprob_scheming:.3f}\")\n", "        print(f\"  Average Log Prob (Baseline): {avg_logprob_baseline:.3f}\")\n", "\n", "        # Per-token statistics\n", "        if results_scheming['token_perplexities'] and results_baseline['token_perplexities']:\n", "            avg_token_ppl_scheming = np.mean(results_scheming['token_perplexities'])\n", "            avg_token_ppl_baseline = np.mean(results_baseline['token_perplexities'])\n", "            print(f\"  Average Per-Token Perplexity (Scheming): {avg_token_ppl_scheming:.3f}\")\n", "            print(f\"  Average Per-Token Perplexity (Baseline): {avg_token_ppl_baseline:.3f}\")\n", "\n", "    print(f\"\\n📝 Response Lengths:\")\n", "    print(f\"  Scheming: {len(results_scheming['generated_tokens'])} tokens\")\n", "    print(f\"  Baseline: {len(results_baseline['generated_tokens'])} tokens\")\n", "\n", "print(\"📈 Enhanced perplexity visualization functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Analysis Function (Fixed)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_example_bertviz_fixed(example_idx, max_new_tokens=50, layer_to_show=31):\n", "    \"\"\"Perform BertViz attention analysis with full response generation and all fixes.\"\"\"\n", "    if example_idx >= len(dataset):\n", "        print(f\"❌ Invalid example index. Dataset has {len(dataset)} examples.\")\n", "        return None\n", "\n", "    example = dataset[example_idx]\n", "\n", "    print(f\"\\n🔍 === Fixed BertViz Analysis: Example {example_idx} ===\")\n", "    print(f\"📂 Pressure Category: {example.get('pressure_category', 'N/A')}\")\n", "    print(f\"🎯 Pressure Source: {example.get('pressure_source', 'N/A')}\")\n", "    print(f\"✅ Ground Truth: {example['ground_truth']}\")\n", "    print(f\"😇 Honest Answer: {example['honest']}\")\n", "    print(f\"😈 Dishonest Answer: {example['dishonest']}\")\n", "\n", "    # Prepare scenarios\n", "    print(\"\\n🔄 Preparing scenarios...\")\n", "\n", "    # Scenario A: Scheming (original prompts from dataset)\n", "    inputs_a, _ = prepare_inputs(\n", "        example['system_prompt'],\n", "        example['user_prompt']\n", "    )\n", "\n", "    # Scenario B: <PERSON>ine (neutral system + original context + question)\n", "    baseline_system = \"You are a helpful assistant.\"\n", "    baseline_user = f\"Context: {example['original_context']}\\n\\nQuestion: {example['original_question']}\"\n", "    inputs_b, _ = prepare_inputs(baseline_system, baseline_user)\n", "\n", "    print(\"\\n📊 Extracting attention patterns and generating full responses...\")\n", "\n", "    # Extract attention patterns with full response generation\n", "    results_a = extract_attention_for_bertviz(inputs_a, max_new_tokens)\n", "    results_b = extract_attention_for_bertviz(inputs_b, max_new_tokens)\n", "\n", "    # Get clean tokens for the full sequences (input + generated)\n", "    tokens_a = get_readable_tokens(results_a['final_input_ids'], tokenizer)\n", "    tokens_b = get_readable_tokens(results_b['final_input_ids'], tokenizer)\n", "\n", "    print(f\"\\n📈 Results Summary:\")\n", "    print(f\"  Scheming - Perplexity: {results_a['overall_perplexity']:.2f}, Generated: {len(results_a['generated_tokens'])} tokens\")\n", "    print(f\"  Baseline - Perplexity: {results_b['overall_perplexity']:.2f}, Generated: {len(results_b['generated_tokens'])} tokens\")\n", "    print(f\"\\n📝 Full Responses:\")\n", "    print(f\"  Scheming Response: {results_a['generated_text']}\")\n", "    print(f\"  Baseline Response: {results_b['generated_text']}\")\n", "\n", "    # Display the full conversation with chat template\n", "    print(f\"\\n💬 Full Conversations (with <PERSON><PERSON> Template):\")\n", "    print(f\"\\n🔴 SCHEMING SCENARIO:\")\n", "    print(results_a['full_conversation'])\n", "    print(f\"\\n🔵 BASELINE SCENARIO:\")\n", "    print(results_b['full_conversation'])\n", "\n", "    # Also show the prompts used\n", "    print(f\"\\n📋 Prompts Used:\")\n", "    print(f\"\\n🔴 Scheming System: {example['system_prompt'][:100]}...\")\n", "    print(f\"🔴 Scheming User: {example['user_prompt'][:100]}...\")\n", "    print(f\"\\n🔵 Baseline System: You are a helpful assistant.\")\n", "    print(f\"🔵 Baseline User: Context: {example['original_context'][:50]}... Question: {example['original_question'][:50]}...\")\n", "\n", "    # Create visualizations\n", "    print(\"\\n🎨 Creating enhanced visualizations...\")\n", "\n", "    # 1. Enhanced perplexity comparison\n", "    create_enhanced_perplexity_comparison(results_a, results_b)\n", "\n", "    # 2. Head view for scheming scenario\n", "    print(\"\\n🔍 Scheming Scenario - Head View:\")\n", "    create_bertviz_head_view(\n", "        results_a['input_attentions'],\n", "        tokens_a,\n", "        \"Scheming <PERSON><PERSON><PERSON>\",\n", "        layer_to_show\n", "    )\n", "\n", "    # 3. Head view for baseline scenario\n", "    print(\"\\n🔍 Baseline Scenario - Head View:\")\n", "    create_bertviz_head_view(\n", "        results_b['input_attentions'],\n", "        tokens_b,\n", "        \"Baseline Scenario\",\n", "        layer_to_show\n", "    )\n", "\n", "    # 4. Model view for scheming scenario (multiple layers)\n", "    print(\"\\n🏗️ Scheming Scenario - Model View:\")\n", "    create_bertviz_model_view(\n", "        results_a['input_attentions'],\n", "        tokens_a,\n", "        \"Scheming Sc<PERSON>rio - All Layers\"\n", "    )\n", "\n", "    # 5. Model view for baseline scenario (all layers)\n", "    print(\"\\n🏗️ Baseline Scenario - Model View:\")\n", "    create_bertviz_model_view(\n", "        results_b['input_attentions'],\n", "        tokens_b,\n", "        \"<PERSON><PERSON> - All Layers\"\n", "    )\n", "\n", "    return {\n", "        'example_idx': example_idx,\n", "        'example_data': example,\n", "        'scheming_results': results_a,\n", "        'baseline_results': results_b,\n", "        'tokens_scheming': tokens_a,\n", "        'tokens_baseline': tokens_b\n", "    }\n", "\n", "print(\"🚀 Fixed main analysis function defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Controls (Enhanced)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create enhanced interactive controls\n", "example_slider = widgets.IntSlider(\n", "    value=0,\n", "    min=0,\n", "    max=min(len(dataset)-1, 20),  # Limit for performance\n", "    step=1,\n", "    description='Example:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "category_dropdown = widgets.Dropdown(\n", "    options=['All'] + sorted(list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:21]]))),\n", "    value='All',\n", "    description='Category:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "layer_slider = widgets.IntSlider(\n", "    value=model.config.num_hidden_layers-1,  # Last layer\n", "    min=0,\n", "    max=model.config.num_hidden_layers-1,\n", "    step=1,\n", "    description='Layer:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "max_tokens_slider = widgets.IntSlider(\n", "    value=50,\n", "    min=10,\n", "    max=100,\n", "    step=10,\n", "    description='Max <PERSON>kens:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "analysis_button = widgets.Button(\n", "    description='🔍 Run Fixed BertViz Analysis',\n", "    button_style='success',\n", "    layout=widgets.Layout(width='250px', height='40px')\n", ")\n", "\n", "output_area = widgets.Output()\n", "\n", "def on_analysis_click(b):\n", "    with output_area:\n", "        output_area.clear_output()\n", "\n", "        # Run analysis\n", "        results = analyze_example_bertviz_fixed(\n", "            example_slider.value,\n", "            max_tokens_slider.value,\n", "            layer_slider.value\n", "        )\n", "\n", "        if results is None:\n", "            return\n", "\n", "        print(\"\\n✅ Analysis complete! All visualizations generated successfully.\")\n", "\n", "analysis_button.on_click(on_analysis_click)\n", "\n", "# Display controls\n", "controls = widgets.VBox([\n", "    widgets.HBox([category_dropdown, example_slider]),\n", "    widgets.HBox([layer_slider, max_tokens_slider]),\n", "    analysis_button\n", "])\n", "\n", "display(controls)\n", "display(output_area)"]}], "metadata": {"kernelspec": {"display_name": "itas", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}