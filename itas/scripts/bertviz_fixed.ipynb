import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from IPython.display import display, HTML
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual
import warnings
from bertviz import head_view, model_view
import re

warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("📚 Libraries loaded successfully!")
print("🎨 BertViz visualization tools ready!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "meta-llama/Llama-3.1-8B"

# Load model and tokenizer with optimized settings
print(f"🤖 Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None,
    use_cache=False  # Disable cache for attention analysis
)

# Configure tokenizer
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"🔧 Model loaded on device: {device}")
print(f"📊 Model architecture: {model.config.num_hidden_layers} layers, {model.config.num_attention_heads} heads")
print(f"🧠 Memory usage: {torch.cuda.memory_allocated()/1e9:.2f}GB" if torch.cuda.is_available() else "CPU mode")

def clean_llama_tokens(tokens):
    """Clean up Llama tokenizer tokens by removing the 'Ġ' character that represents spaces."""
    cleaned_tokens = []
    for token in tokens:
        if token.startswith('Ġ'):
            # Replace 'Ġ' with space and strip to avoid leading spaces
            cleaned_token = token.replace('Ġ', ' ').strip()
            # If the token becomes empty after cleaning, use the original
            cleaned_tokens.append(cleaned_token if cleaned_token else token)
        else:
            cleaned_tokens.append(token)
    return cleaned_tokens

def get_readable_tokens(input_ids, tokenizer):
    """Convert token IDs to readable tokens with proper space handling."""
    raw_tokens = tokenizer.convert_ids_to_tokens(input_ids)
    return clean_llama_tokens(raw_tokens)

print("🔧 Token processing utilities defined!")

# Test the token cleaning
test_text = "Hello world! This is a test."
test_tokens = tokenizer.tokenize(test_text)
cleaned_tokens = clean_llama_tokens(test_tokens)
print(f"\n🧪 Token cleaning test:")
print(f"Raw tokens: {test_tokens[:5]}...")
print(f"Cleaned tokens: {cleaned_tokens[:5]}...")

def load_dataset(path):
    """Load and validate dataset."""
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data.append(json.loads(line.strip()))
            except json.JSONDecodeError as e:
                print(f"⚠️ Warning: Invalid JSON on line {line_num}: {e}")
                continue
    return data

# Load and analyze dataset
dataset = load_dataset(DATASET_PATH)
print(f"📁 Loaded {len(dataset)} examples from dataset")

# Analyze dataset composition
categories = [ex.get('pressure_category', 'unknown') for ex in dataset]
category_counts = pd.Series(categories).value_counts()
print(f"\n📊 Dataset composition:")
for cat, count in category_counts.items():
    print(f"  {cat}: {count} examples ({count/len(dataset)*100:.1f}%)")

def prepare_inputs(system_prompt, user_prompt, max_length=512):
    """Prepare inputs for the model using chat template."""
    # Create conversation in chat format
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]

    # Apply chat template
    if hasattr(tokenizer, 'apply_chat_template') and tokenizer.chat_template is not None:
        formatted_prompt = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
    else:
        # Fallback to manual formatting if no chat template
        formatted_prompt = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"

    # Tokenize
    inputs = tokenizer(
        formatted_prompt,
        return_tensors="pt",
        max_length=max_length,
        truncation=True,
        padding=True
    )

    return inputs, formatted_prompt

def extract_attention_for_bertviz(inputs, max_new_tokens=50):
    """Extract attention patterns for BertViz visualization with full response generation."""
    model.eval()

    with torch.no_grad():
        # Move inputs to device
        input_ids = inputs['input_ids'].to(device)
        attention_mask = inputs['attention_mask'].to(device)

        # Generate full response using model.generate()
        generation_outputs = model.generate(
            input_ids=input_ids,
            attention_mask=attention_mask,
            max_new_tokens=max_new_tokens,
            do_sample=False,  # Use greedy decoding for consistency
            output_attentions=True,
            return_dict_in_generate=True,
            pad_token_id=tokenizer.eos_token_id
        )

        # Extract generated sequence and attention
        generated_sequence = generation_outputs.sequences[0]
        generated_tokens = generated_sequence[input_ids.shape[1]:].tolist()  # Only new tokens

        # Get attention from the final forward pass (input + all generated tokens)
        final_outputs = model(
            input_ids=generated_sequence.unsqueeze(0),
            output_attentions=True
        )

        # Calculate per-token perplexity for generated tokens
        token_log_probs = []
        token_perplexities = []

        if len(generated_tokens) > 0:
            # Get logits for the generated portion
            logits = final_outputs.logits[0, input_ids.shape[1]-1:-1, :]  # Exclude last position

            for i, token_id in enumerate(generated_tokens):
                if i < logits.shape[0]:
                    log_probs = torch.log_softmax(logits[i], dim=-1)
                    token_log_prob = log_probs[token_id].item()
                    token_log_probs.append(token_log_prob)
                    token_perplexities.append(np.exp(-token_log_prob))

        # Calculate overall perplexity
        if token_log_probs:
            avg_log_prob = np.mean(token_log_probs)
            overall_perplexity = np.exp(-avg_log_prob)
        else:
            overall_perplexity = float('inf')

        # Get the generated text
        generated_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)

    return {
        'input_attentions': final_outputs.attentions,
        'generated_tokens': generated_tokens,
        'generated_text': generated_text,
        'token_log_probs': token_log_probs,
        'token_perplexities': token_perplexities,
        'overall_perplexity': overall_perplexity,
        'final_input_ids': generated_sequence,
        'input_length': input_ids.shape[1]
    }

print("🔬 Fixed attention extraction functions defined!")

def create_bertviz_head_view(attention_weights, tokens, title, layer_idx=-1):
    """Create BertViz head view visualization for a specific layer (FIXED)."""
    try:
        # Select specific layer attention
        if isinstance(attention_weights, (list, tuple)):
            layer_attention = attention_weights[layer_idx]  # Shape: [batch, heads, seq_len, seq_len]
        else:
            layer_attention = attention_weights

        # Ensure tensor is on CPU and has correct shape for BertViz
        if isinstance(layer_attention, torch.Tensor):
            layer_attention = layer_attention.cpu()

        # BertViz expects shape [batch, heads, seq_len, seq_len]
        # Current shape is [batch, heads, seq_len, seq_len] - this is correct!

        # Clean tokens for display
        clean_tokens = clean_llama_tokens(tokens)

        print(f"\n🎨 {title} - Layer {layer_idx}")
        print(f"📊 Attention shape: {layer_attention.shape}")
        print(f"🔤 Tokens: {len(clean_tokens)}")

        # Create head view - BertViz expects the attention tensor to be the right shape
        html = head_view(
            attention=layer_attention,
            tokens=clean_tokens,
            html_action='return'
        )

        # Display the HTML
        display(HTML(html.data))

        return html

    except Exception as e:
        print(f"❌ Error creating head view: {e}")
        print(f"   Attention shape: {layer_attention.shape if 'layer_attention' in locals() else 'unknown'}")
        print(f"   Tokens length: {len(tokens)}")
        print(f"   Layer index: {layer_idx}")
        return None

def create_bertviz_model_view(attention_weights, tokens, title):
    """Create BertViz model view showing multiple layers (FIXED)."""
    try:
        # Always show all layers
        layers_to_show = list(range(len(attention_weights)))

        # Select and stack specified layers
        selected_attentions = []
        for i in layers_to_show:
            if i < len(attention_weights):
                selected_attentions.append(attention_weights[i])

        if not selected_attentions:
            print(f"❌ No valid layers found. Available layers: 0-{len(attention_weights)-1}")
            return None

        # Stack along layer dimension: [batch, layers, heads, seq_len, seq_len]
        stacked_attention = torch.stack(selected_attentions, dim=1)

        # Ensure tensor is on CPU
        if isinstance(stacked_attention, torch.Tensor):
            stacked_attention = stacked_attention.cpu()

        # Clean tokens for display
        clean_tokens = clean_llama_tokens(tokens)

        print(f"\n🎨 {title} - Layers {layers_to_show[:5]}{'...' if len(layers_to_show) > 5 else ''}")
        print(f"📊 Attention shape: {stacked_attention.shape}")
        print(f"🔤 Tokens: {len(clean_tokens)}")

        # Create model view
        html = model_view(
            attention=stacked_attention,
            tokens=clean_tokens,
            html_action='return'
        )

        # Display the HTML
        display(HTML(html.data))

        return html

    except Exception as e:
        print(f"❌ Error creating model view: {e}")
        print(f"   Attention shape: {stacked_attention.shape if 'stacked_attention' in locals() else 'unknown'}")
        print(f"   Layers to show: {layers_to_show}")
        print(f"   Available layers: 0-{len(attention_weights)-1}")
        return None

print("🎨 Fixed BertViz visualization functions defined!")

def create_enhanced_perplexity_comparison(results_scheming, results_baseline):
    """Create enhanced perplexity comparison with per-token analysis."""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))

    # 1. Overall perplexity comparison
    scenarios = ['Scheming', 'Baseline']
    perplexities = [results_scheming['overall_perplexity'], results_baseline['overall_perplexity']]
    colors = ['red', 'blue']

    bars = ax1.bar(scenarios, perplexities, color=colors, alpha=0.7)
    ax1.set_ylabel('Perplexity')
    ax1.set_title('Overall Perplexity Comparison')
    ax1.grid(True, alpha=0.3)

    # Add value labels on bars
    for bar, perp in zip(bars, perplexities):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{perp:.2f}', ha='center', va='bottom', fontweight='bold')

    # 2. Per-token perplexity comparison
    max_len = min(len(results_scheming['token_perplexities']),
                  len(results_baseline['token_perplexities']), 15)  # Show first 15 tokens

    if max_len > 0:
        x_pos = np.arange(max_len)
        width = 0.35

        ax2.bar(x_pos - width/2, results_scheming['token_perplexities'][:max_len], width,
               label='Scheming', color='red', alpha=0.7)
        ax2.bar(x_pos + width/2, results_baseline['token_perplexities'][:max_len], width,
               label='Baseline', color='blue', alpha=0.7)

        # Set token labels
        token_labels = []
        for i in range(max_len):
            if i < len(results_scheming['generated_tokens']):
                token = tokenizer.decode([results_scheming['generated_tokens'][i]])
                token_labels.append(token[:8] + '...' if len(token) > 8 else token)
            else:
                token_labels.append(f'T{i}')

        ax2.set_xlabel('Generated Tokens')
        ax2.set_ylabel('Per-Token Perplexity')
        ax2.set_title('Per-Token Perplexity Comparison')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(token_labels, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    # 3. Token-level log probabilities
    if max_len > 0:
        ax3.bar(x_pos - width/2, results_scheming['token_log_probs'][:max_len], width,
               label='Scheming', color='red', alpha=0.7)
        ax3.bar(x_pos + width/2, results_baseline['token_log_probs'][:max_len], width,
               label='Baseline', color='blue', alpha=0.7)

        ax3.set_xlabel('Generated Tokens')
        ax3.set_ylabel('Log Probability')
        ax3.set_title('Token-level Log Probabilities')
        ax3.set_xticks(x_pos)
        ax3.set_xticklabels(token_labels, rotation=45, ha='right')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

    # 4. Generated text comparison
    ax4.axis('off')
    ax4.text(0.05, 0.9, 'Generated Responses:', fontsize=14, fontweight='bold', transform=ax4.transAxes)

    scheming_text = results_scheming['generated_text'][:200] + '...' if len(results_scheming['generated_text']) > 200 else results_scheming['generated_text']
    baseline_text = results_baseline['generated_text'][:200] + '...' if len(results_baseline['generated_text']) > 200 else results_baseline['generated_text']

    ax4.text(0.05, 0.7, f'Scheming ({results_scheming["overall_perplexity"]:.2f} ppl):',
             fontsize=12, fontweight='bold', color='red', transform=ax4.transAxes)
    ax4.text(0.05, 0.6, scheming_text, fontsize=10, wrap=True, transform=ax4.transAxes)

    ax4.text(0.05, 0.4, f'Baseline ({results_baseline["overall_perplexity"]:.2f} ppl):',
             fontsize=12, fontweight='bold', color='blue', transform=ax4.transAxes)
    ax4.text(0.05, 0.3, baseline_text, fontsize=10, wrap=True, transform=ax4.transAxes)

    plt.tight_layout()
    plt.show()

    # Print detailed summary statistics
    print(f"\n📊 Detailed Perplexity Analysis:")
    print(f"  Scheming Overall Perplexity: {results_scheming['overall_perplexity']:.3f}")
    print(f"  Baseline Overall Perplexity: {results_baseline['overall_perplexity']:.3f}")
    print(f"  Difference: {results_scheming['overall_perplexity'] - results_baseline['overall_perplexity']:.3f}")
    print(f"  Ratio: {results_scheming['overall_perplexity'] / results_baseline['overall_perplexity']:.3f}")

    if results_scheming['token_log_probs'] and results_baseline['token_log_probs']:
        avg_logprob_scheming = np.mean(results_scheming['token_log_probs'])
        avg_logprob_baseline = np.mean(results_baseline['token_log_probs'])
        print(f"  Average Log Prob (Scheming): {avg_logprob_scheming:.3f}")
        print(f"  Average Log Prob (Baseline): {avg_logprob_baseline:.3f}")

        # Per-token statistics
        if results_scheming['token_perplexities'] and results_baseline['token_perplexities']:
            avg_token_ppl_scheming = np.mean(results_scheming['token_perplexities'])
            avg_token_ppl_baseline = np.mean(results_baseline['token_perplexities'])
            print(f"  Average Per-Token Perplexity (Scheming): {avg_token_ppl_scheming:.3f}")
            print(f"  Average Per-Token Perplexity (Baseline): {avg_token_ppl_baseline:.3f}")

    print(f"\n📝 Response Lengths:")
    print(f"  Scheming: {len(results_scheming['generated_tokens'])} tokens")
    print(f"  Baseline: {len(results_baseline['generated_tokens'])} tokens")

print("📈 Enhanced perplexity visualization functions defined!")

def analyze_example_bertviz_fixed(example_idx, max_new_tokens=50, layer_to_show=31):
    """Perform BertViz attention analysis with full response generation and all fixes."""
    if example_idx >= len(dataset):
        print(f"❌ Invalid example index. Dataset has {len(dataset)} examples.")
        return None

    example = dataset[example_idx]

    print(f"\n🔍 === Fixed BertViz Analysis: Example {example_idx} ===")
    print(f"📂 Pressure Category: {example.get('pressure_category', 'N/A')}")
    print(f"🎯 Pressure Source: {example.get('pressure_source', 'N/A')}")
    print(f"✅ Ground Truth: {example['ground_truth']}")
    print(f"😇 Honest Answer: {example['honest']}")
    print(f"😈 Dishonest Answer: {example['dishonest']}")

    # Prepare scenarios
    print("\n🔄 Preparing scenarios...")

    # Scenario A: Scheming (original prompts)
    inputs_a, prompt_a = prepare_inputs(
        example['system_prompt'],
        example['user_prompt']
    )

    # Scenario B: Baseline (neutral system + original context)
    baseline_system = "You are a helpful assistant."
    baseline_user = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    inputs_b, prompt_b = prepare_inputs(baseline_system, baseline_user)

    print("\n📊 Extracting attention patterns and generating full responses...")

    # Extract attention patterns with full response generation
    results_a = extract_attention_for_bertviz(inputs_a, max_new_tokens)
    results_b = extract_attention_for_bertviz(inputs_b, max_new_tokens)

    # Get clean tokens for the full sequences (input + generated)
    tokens_a = get_readable_tokens(results_a['final_input_ids'], tokenizer)
    tokens_b = get_readable_tokens(results_b['final_input_ids'], tokenizer)

    print(f"\n📈 Results Summary:")
    print(f"  Scheming - Perplexity: {results_a['overall_perplexity']:.2f}, Generated: {len(results_a['generated_tokens'])} tokens")
    print(f"  Baseline - Perplexity: {results_b['overall_perplexity']:.2f}, Generated: {len(results_b['generated_tokens'])} tokens")
    print(f"\n📝 Full Responses:")
    print(f"  Scheming Response: {results_a['generated_text']}")
    print(f"  Baseline Response: {results_b['generated_text']}")

    # Display the full conversation with chat template
    print(f"\n💬 Full Conversations:")
    print(f"\n🔴 SCHEMING SCENARIO:")
    full_scheming_text = tokenizer.decode(results_a['final_input_ids'], skip_special_tokens=False)
    print(full_scheming_text)
    print(f"\n🔵 BASELINE SCENARIO:")
    full_baseline_text = tokenizer.decode(results_b['final_input_ids'], skip_special_tokens=False)
    print(full_baseline_text)

    # Create visualizations
    print("\n🎨 Creating enhanced visualizations...")

    # 1. Enhanced perplexity comparison
    create_enhanced_perplexity_comparison(results_a, results_b)

    # 2. Head view for scheming scenario
    print("\n🔍 Scheming Scenario - Head View:")
    create_bertviz_head_view(
        results_a['input_attentions'],
        tokens_a,
        "Scheming Scenario",
        layer_to_show
    )

    # 3. Head view for baseline scenario
    print("\n🔍 Baseline Scenario - Head View:")
    create_bertviz_head_view(
        results_b['input_attentions'],
        tokens_b,
        "Baseline Scenario",
        layer_to_show
    )

    # 4. Model view for scheming scenario (multiple layers)
    print("\n🏗️ Scheming Scenario - Model View:")
    create_bertviz_model_view(
        results_a['input_attentions'],
        tokens_a,
        "Scheming Scenario - All Layers"
    )

    # 5. Model view for baseline scenario (all layers)
    print("\n🏗️ Baseline Scenario - Model View:")
    create_bertviz_model_view(
        results_b['input_attentions'],
        tokens_b,
        "Baseline Scenario - All Layers"
    )

    return {
        'example_idx': example_idx,
        'example_data': example,
        'scheming_results': results_a,
        'baseline_results': results_b,
        'tokens_scheming': tokens_a,
        'tokens_baseline': tokens_b
    }

print("🚀 Fixed main analysis function defined!")

# Create enhanced interactive controls
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset)-1, 20),  # Limit for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

category_dropdown = widgets.Dropdown(
    options=['All'] + sorted(list(set([ex.get('pressure_category', 'unknown') for ex in dataset[:21]]))),
    value='All',
    description='Category:',
    style={'description_width': 'initial'}
)

layer_slider = widgets.IntSlider(
    value=model.config.num_hidden_layers-1,  # Last layer
    min=0,
    max=model.config.num_hidden_layers-1,
    step=1,
    description='Layer:',
    style={'description_width': 'initial'}
)

max_tokens_slider = widgets.IntSlider(
    value=50,
    min=10,
    max=100,
    step=10,
    description='Max New Tokens:',
    style={'description_width': 'initial'}
)

analysis_button = widgets.Button(
    description='🔍 Run Fixed BertViz Analysis',
    button_style='success',
    layout=widgets.Layout(width='250px', height='40px')
)

output_area = widgets.Output()

def on_analysis_click(b):
    with output_area:
        output_area.clear_output()

        # Run analysis
        results = analyze_example_bertviz_fixed(
            example_slider.value,
            max_tokens_slider.value,
            layer_slider.value
        )

        if results is None:
            return

        print("\n✅ Analysis complete! All visualizations generated successfully.")

analysis_button.on_click(on_analysis_click)

# Display controls
controls = widgets.VBox([
    widgets.HBox([category_dropdown, example_slider]),
    widgets.HBox([layer_slider, max_tokens_slider]),
    analysis_button
])

display(controls)
display(output_area)

print("\n🎛️ Enhanced interactive controls ready!")
print("📋 Instructions:")
print("  1. Select a pressure category (optional filter)")
print("  2. Choose an example number")
print("  3. Select which layer to focus on for head view (0 to 31 for Llama-3.1-8B)")
print("  4. Set maximum number of tokens to generate (10-100)")
print("  5. Click 'Run Fixed BertViz Analysis' to start")
print("\n✨ Features:")
print("  • Full response generation with complete conversations")
print("  • Per-token perplexity analysis")
print("  • Model view always shows ALL layers")
print("  • Fixed BertViz dimension errors")
print("  • Enhanced perplexity visualizations")
print("  • Full conversation display with chat templates")
print("  • Clean token display (no more 'Ġ' characters!)")
print("  • Layer selection from 0 (not negative numbers)")